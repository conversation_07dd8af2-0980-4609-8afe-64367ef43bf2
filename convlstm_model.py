import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np
import os
import time
from tqdm import tqdm
import matplotlib.pyplot as plt
import xarray as xr

class ConvLSTMCell(nn.Module):
    """
    ConvLSTM单元
    """
    def __init__(self, input_dim, hidden_dim, kernel_size, bias=True):
        """
        初始化ConvLSTM单元
        
        参数:
            input_dim: 输入特征通道数
            hidden_dim: 隐藏状态通道数
            kernel_size: 卷积核大小
            bias: 是否使用偏置
        """
        super(ConvLSTMCell, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2
        self.bias = bias
        
        # 所有门控的卷积操作合并为一个大的卷积
        self.conv = nn.Conv2d(
            in_channels=self.input_dim + self.hidden_dim,
            out_channels=4 * self.hidden_dim,  # 4个门: 输入门、遗忘门、单元状态、输出门
            kernel_size=self.kernel_size,
            padding=self.padding,
            bias=self.bias
        )
    
    def forward(self, input_tensor, cur_state):
        """
        前向传播
        
        参数:
            input_tensor: 输入张量 [batch, input_dim, height, width]
            cur_state: 当前状态 (h, c)
            
        返回:
            h_next, c_next: 下一个隐藏状态和单元状态
        """
        h_cur, c_cur = cur_state
        
        # 连接输入和隐藏状态
        combined = torch.cat([input_tensor, h_cur], dim=1)
        
        # 卷积操作
        combined_conv = self.conv(combined)
        
        # 分离门控值
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)
        
        # 应用激活函数和门控机制
        i = torch.sigmoid(cc_i)  # 输入门
        f = torch.sigmoid(cc_f)  # 遗忘门
        o = torch.sigmoid(cc_o)  # 输出门
        g = torch.tanh(cc_g)     # 候选单元状态
        
        # 更新单元状态和隐藏状态
        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next
    
    def init_hidden(self, batch_size, image_size):
        """
        初始化隐藏状态
        
        参数:
            batch_size: 批次大小
            image_size: 图像尺寸 (height, width)
            
        返回:
            (h, c): 初始隐藏状态和单元状态
        """
        height, width = image_size
        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device),
                torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device))


class ConvLSTM(nn.Module):
    """
    多层ConvLSTM模型
    """
    def __init__(self, input_dim, hidden_dims, kernel_size, num_layers, batch_first=True, bias=True, return_all_layers=False):
        """
        初始化ConvLSTM模型
        
        参数:
            input_dim: 输入特征通道数
            hidden_dims: 每层隐藏状态通道数的列表
            kernel_size: 卷积核大小
            num_layers: 层数
            batch_first: 输入是否为batch优先格式
            bias: 是否使用偏置
            return_all_layers: 是否返回所有层的输出
        """
        super(ConvLSTM, self).__init__()
        
        # 错误检查
        if isinstance(kernel_size, tuple):
            kernel_size = kernel_size[0]
        
        if not isinstance(hidden_dims, list):
            hidden_dims = [hidden_dims] * num_layers
        
        if len(hidden_dims) != num_layers:
            raise ValueError("hidden_dims的长度必须等于num_layers")
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.batch_first = batch_first
        self.bias = bias
        self.return_all_layers = return_all_layers
        
        # 创建ConvLSTM层
        cell_list = []
        for i in range(self.num_layers):
            cur_input_dim = self.input_dim if i == 0 else self.hidden_dims[i-1]
            cell_list.append(
                ConvLSTMCell(
                    input_dim=cur_input_dim,
                    hidden_dim=self.hidden_dims[i],
                    kernel_size=self.kernel_size,
                    bias=self.bias
                )
            )
        
        self.cell_list = nn.ModuleList(cell_list)
    
    def forward(self, input_tensor, hidden_state=None):
        """
        前向传播
        
        参数:
            input_tensor: 输入张量 [batch, seq_len, channels, height, width] 或 [seq_len, batch, channels, height, width]
            hidden_state: 初始隐藏状态，如果为None则自动初始化
            
        返回:
            layer_output_list: 每层的输出列表
            last_state_list: 每层的最终状态列表
        """
        # 确保输入格式正确
        if not self.batch_first:
            # (seq_len, batch, channels, height, width) -> (batch, seq_len, channels, height, width)
            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)
        
        # 获取维度信息
        batch_size, seq_len, _, height, width = input_tensor.size()
        
        # 初始化隐藏状态
        if hidden_state is None:
            hidden_state = self._init_hidden(batch_size, (height, width))
        
        layer_output_list = []
        last_state_list = []
        
        # 对每一层进行处理
        cur_layer_input = input_tensor
        for layer_idx in range(self.num_layers):
            h, c = hidden_state[layer_idx]
            output_inner = []
            
            # 对序列中的每一个时间步进行处理
            for t in range(seq_len):
                h, c = self.cell_list[layer_idx](
                    input_tensor=cur_layer_input[:, t, :, :, :],
                    cur_state=[h, c]
                )
                output_inner.append(h)
            
            # 将输出堆叠为一个张量
            layer_output = torch.stack(output_inner, dim=1)
            cur_layer_input = layer_output
            
            layer_output_list.append(layer_output)
            last_state_list.append([h, c])
        
        # 根据需要返回结果
        if not self.return_all_layers:
            layer_output_list = layer_output_list[-1:]
            last_state_list = last_state_list[-1:]
        
        return layer_output_list, last_state_list
    
    def _init_hidden(self, batch_size, image_size):
        """
        初始化所有层的隐藏状态
        
        参数:
            batch_size: 批次大小
            image_size: 图像尺寸 (height, width)
            
        返回:
            init_states: 初始状态列表
        """
        init_states = []
        for i in range(self.num_layers):
            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size))
        return init_states


class SSTConvLSTMModel(nn.Module):
    """
    用于SST预测的ConvLSTM模型
    """
    def __init__(self, num_imfs, hidden_dims=[64, 64], kernel_size=3, dropout=0.2):
        """
        初始化SST预测模型
        
        参数:
            num_imfs: IMF组件数量（输入通道数）
            hidden_dims: ConvLSTM隐藏层维度列表
            kernel_size: 卷积核大小
            dropout: Dropout比率
        """
        super(SSTConvLSTMModel, self).__init__()
        
        self.num_imfs = num_imfs
        
        # ConvLSTM层
        self.convlstm = ConvLSTM(
            input_dim=num_imfs,
            hidden_dims=hidden_dims,
            kernel_size=kernel_size,
            num_layers=len(hidden_dims),
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 批归一化层
        self.bn = nn.BatchNorm2d(hidden_dims[-1])
        
        # Dropout层
        self.dropout = nn.Dropout2d(dropout)
        
        # 输出卷积层
        self.output_conv = nn.Conv2d(
            in_channels=hidden_dims[-1],
            out_channels=num_imfs,  # 输出与输入相同数量的IMF通道
            kernel_size=1
        )
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入张量 [batch, num_imfs, seq_len, height, width]
            
        返回:
            output: 预测结果 [batch, num_imfs, height, width]
        """
        # 调整输入形状以适应ConvLSTM
        # [batch, num_imfs, seq_len, height, width] -> [batch, seq_len, num_imfs, height, width]
        x = x.permute(0, 2, 1, 3, 4)
        
        # 通过ConvLSTM
        layer_output_list, _ = self.convlstm(x)
        output = layer_output_list[0]
        
        # 只取最后一个时间步的输出
        output = output[:, -1]
        
        # 批归一化
        output = self.bn(output)
        
        # Dropout
        output = self.dropout(output)
        
        # 输出卷积
        output = self.output_conv(output)
        
        return output


def train_model(model, train_loader, val_loader, device, num_epochs=50, 
               learning_rate=0.001, weight_decay=1e-5, patience=10, 
               checkpoint_dir='checkpoints'):
    """
    训练SST预测模型
    
    参数:
        model: 模型实例
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 计算设备
        num_epochs: 训练轮数
        learning_rate: 学习率
        weight_decay: 权重衰减
        patience: 早停耐心值
        checkpoint_dir: 模型检查点保存目录
        
    返回:
        model: 训练好的模型
        history: 训练历史记录
    """
    # 创建检查点目录
    if not os.path.exists(checkpoint_dir):
        os.makedirs(checkpoint_dir)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'val_mse': [],
        'val_mae': [],
        'val_rmse': []
    }
    
    # 早停设置
    best_val_loss = float('inf')
    early_stop_counter = 0
    
    # 训练循环
    for epoch in range(num_epochs):
        start_time = time.time()
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        for inputs, targets in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]"):
            # 将数据移到设备上
            inputs = inputs.to(device)
            targets = targets.to(device)
            
            # 清除梯度
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(inputs)
            
            # 计算损失（只考虑预测的第一个时间步）
            loss = criterion(outputs, targets[:, :, 0])
            
            # 反向传播和优化
            loss.backward()
            optimizer.step()
            
            # 累计损失
            train_loss += loss.item()
            train_batches += 1
        
        # 计算平均训练损失
        avg_train_loss = train_loss / train_batches
        history['train_loss'].append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_mae = 0.0
        all_mse = 0.0
        
        with torch.no_grad():
            for inputs, targets in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]"):
                # 将数据移到设备上
                inputs = inputs.to(device)
                targets = targets.to(device)
                
                # 前向传播
                outputs = model(inputs)
                
                # 计算损失（只考虑预测的第一个时间步）
                loss = criterion(outputs, targets[:, :, 0])
                
                # 计算评估指标
                mae = torch.mean(torch.abs(outputs - targets[:, :, 0]))
                mse = torch.mean((outputs - targets[:, :, 0]) ** 2)
                
                # 累计损失和指标
                val_loss += loss.item()
                all_mae += mae.item()
                all_mse += mse.item()
                val_batches += 1
        
        # 计算平均验证损失和指标
        avg_val_loss = val_loss / val_batches
        avg_mae = all_mae / val_batches
        avg_mse = all_mse / val_batches
        avg_rmse = np.sqrt(avg_mse)
        
        # 更新学习率
        scheduler.step(avg_val_loss)
        
        # 记录验证结果
        history['val_loss'].append(avg_val_loss)
        history['val_mae'].append(avg_mae)
        history['val_mse'].append(avg_mse)
        history['val_rmse'].append(avg_rmse)
        
        # 打印进度
        elapsed_time = time.time() - start_time
        print(f"Epoch {epoch+1}/{num_epochs} - "
              f"Train Loss: {avg_train_loss:.6f}, "
              f"Val Loss: {avg_val_loss:.6f}, "
              f"Val MAE: {avg_mae:.6f}, "
              f"Val MSE: {avg_mse:.6f}, "
              f"Val RMSE: {avg_rmse:.6f}, "
              f"Time: {elapsed_time:.2f}s")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': best_val_loss,
                'val_mae': avg_mae,
                'val_mse': avg_mse,
                'val_rmse': avg_rmse
            }, os.path.join(checkpoint_dir, 'best_model.pth'))
            print(f"保存最佳模型，验证损失: {best_val_loss:.6f}")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            print(f"验证损失未改善，计数器: {early_stop_counter}/{patience}")
        
        # 早停检查
        if early_stop_counter >= patience:
            print(f"早停触发，在{epoch+1}轮停止训练")
            break
    
    # 加载最佳模型
    checkpoint = torch.load(os.path.join(checkpoint_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    print(f"加载最佳模型，验证损失: {checkpoint['val_loss']:.6f}")
    
    return model, history


def evaluate_model(model, test_loader, device):
    """
    评估模型性能
    
    参数:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    返回:
        metrics: 评估指标字典
    """
    model.eval()
    all_mse = 0.0
    all_mae = 0.0
    test_batches = 0
    
    with torch.no_grad():
        for inputs, targets in tqdm(test_loader, desc="评估模型"):
            # 将数据移到设备上
            inputs = inputs.to(device)
            targets = targets.to(device)
            
            # 前向传播
            outputs = model(inputs)
            
            # 计算评估指标
            mae = torch.mean(torch.abs(outputs - targets[:, :, 0]))
            mse = torch.mean((outputs - targets[:, :, 0]) ** 2)
            
            # 累计指标
            all_mae += mae.item()
            all_mse += mse.item()
            test_batches += 1
    
    # 计算平均指标
    avg_mae = all_mae / test_batches
    avg_mse = all_mse / test_batches
    avg_rmse = np.sqrt(avg_mse)
    
    # 构建指标字典
    metrics = {
        'mae': avg_mae,
        'mse': avg_mse,
        'rmse': avg_rmse
    }
    
    print(f"测试结果 - MAE: {avg_mae:.6f}, MSE: {avg_mse:.6f}, RMSE: {avg_rmse:.6f}")
    
    return metrics


def plot_training_history(history, save_path=None):
    """
    绘制训练历史记录
    
    参数:
        history: 训练历史记录
        save_path: 保存路径，如果为None则显示图像
    """
    plt.figure(figsize=(15, 10))
    
    # 绘制损失曲线
    plt.subplot(2, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练和验证损失')
    plt.legend()
    plt.grid(True)
    
    # 绘制MSE曲线
    plt.subplot(2, 2, 2)
    plt.plot(history['val_mse'], label='验证MSE')
    plt.xlabel('轮次')
    plt.ylabel('MSE')
    plt.title('验证MSE')
    plt.legend()
    plt.grid(True)
    
    # 绘制MAE曲线
    plt.subplot(2, 2, 3)
    plt.plot(history['val_mae'], label='验证MAE')
    plt.xlabel('轮次')
    plt.ylabel('MAE')
    plt.title('验证MAE')
    plt.legend()
    plt.grid(True)
    
    # 绘制RMSE曲线
    plt.subplot(2, 2, 4)
    plt.plot(history['val_rmse'], label='验证RMSE')
    plt.xlabel('轮次')
    plt.ylabel('RMSE')
    plt.title('验证RMSE')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"训练历史记录图已保存到 {save_path}")
    else:
        plt.show()


def visualize_predictions(model, test_loader, device, num_samples=5, save_dir=None):
    """
    可视化模型预测结果
    
    参数:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 计算设备
        num_samples: 要可视化的样本数量
        save_dir: 保存目录，如果为None则显示图像
    """
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    model.eval()
    samples_visualized = 0
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            if samples_visualized >= num_samples:
                break
            
            # 将数据移到设备上
            inputs = inputs.to(device)
            targets = targets.to(device)
            
            # 前向传播
            outputs = model(inputs)
            
            # 可视化批次中的每个样本
            for i in range(min(inputs.size(0), num_samples - samples_visualized)):
                # 将预测和目标转移到CPU
                pred = outputs[i].cpu().numpy()
                true = targets[i, :, 0].cpu().numpy()
                
                # 计算所有IMF的总和
                pred_sum = np.sum(pred, axis=0)
                true_sum = np.sum(true, axis=0)
                
                # 创建图像
                plt.figure(figsize=(15, 10))
                
                # 绘制预测的总和
                plt.subplot(1, 3, 1)
                plt.imshow(pred_sum, cmap='viridis')
                plt.colorbar()
                plt.title('预测的SST')
                
                # 绘制真实的总和
                plt.subplot(1, 3, 2)
                plt.imshow(true_sum, cmap='viridis')
                plt.colorbar()
                plt.title('真实的SST')
                
                # 绘制差异
                plt.subplot(1, 3, 3)
                plt.imshow(true_sum - pred_sum, cmap='RdBu_r')
                plt.colorbar()
                plt.title('差异 (真实 - 预测)')
                
                plt.tight_layout()
                
                if save_dir:
                    plt.savefig(os.path.join(save_dir, f'prediction_{samples_visualized+1}.png'))
                    plt.close()
                else:
                    plt.show()
                
                samples_visualized += 1
                if samples_visualized >= num_samples:
                    break


def main():
    # 设置随机种子以保证可重复性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 数据集参数
    imf_dir = 'meemd_spatiotemporal_results/imf_components'
    batch_size = 4  # 根据GPU内存调整
    input_seq_len = 14  # 14天的历史数据
    pred_seq_len = 1   # 预测未来1天
    
    # 加载元数据以获取IMF数量
    metadata_path = os.path.join(os.path.dirname(imf_dir), 'meemd_metadata.npy')
    metadata = np.load(metadata_path, allow_pickle=True).item()
    num_imfs = metadata['num_imfs']
    print(f"IMF组件数量: {num_imfs}")
    
    # 创建数据加载器
    print("创建数据加载器...")
    from dataset import create_sst_dataloaders  # 假设上一步创建的数据集类在dataset.py中
    
    train_loader, val_loader, test_loader = create_sst_dataloaders(
        imf_dir=imf_dir,
        batch_size=batch_size,
        input_seq_len=input_seq_len,
        pred_seq_len=pred_seq_len,
        num_workers=4,  # 根据CPU核心数调整
        cache_data=False  # 如果内存足够大，可以设置为True
    )
    
    # 创建模型
    print("创建ConvLSTM模型...")
    model = SSTConvLSTMModel(
        num_imfs=num_imfs,
        hidden_dims=[64, 128],  # 隐藏层维度
        kernel_size=3,          # 卷积核大小
        dropout=0.3             # Dropout比率
    ).to(device)
    
    # 打印模型结构
    print(model)
    
    # 训练模型
    print("开始训练模型...")
    trained_model, history = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        device=device,
        num_epochs=50,           # 训练轮数
        learning_rate=0.001,     # 学习率
        weight_decay=1e-5,       # 权重衰减
        patience=10,             # 早停耐心值
        checkpoint_dir='sst_convlstm_checkpoints'
    )
    
    # 绘制训练历史记录
    print("绘制训练历史记录...")
    plot_training_history(history, save_path='training_history.png')
    
    # 评估模型
    print("评估模型性能...")
    metrics = evaluate_model(trained_model, test_loader, device)
    
    # 可视化预测结果
    print("可视化预测结果...")
    visualize_predictions(
        model=trained_model,
        test_loader=test_loader,
        device=device,
        num_samples=5,
        save_dir='prediction_visualizations'
    )
    
    print("完成!")

if __name__ == "__main__":
    main()
