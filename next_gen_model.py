"""
下一代MEEMD-ConvLSTM模型
基于分解-预测-重构思路的先进架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Optional, Tuple
import math

class SpatialAttention(nn.Module):
    """空间注意力机制"""
    
    def __init__(self, in_channels: int, reduction: int = 16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        attention = self.sigmoid(avg_out + max_out)
        return x * attention

class ChannelAttention(nn.Module):
    """通道注意力机制（用于IMF分量）"""
    
    def __init__(self, num_channels: int, reduction: int = 4):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)  # 3D池化用于时空数据
        self.max_pool = nn.AdaptiveMaxPool3d(1)
        
        self.fc = nn.Sequential(
            nn.Linear(num_channels, num_channels // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(num_channels // reduction, num_channels),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [batch, channels, time, height, width]
        b, c, t, h, w = x.size()
        
        avg_out = self.avg_pool(x).view(b, c)
        max_out = self.max_pool(x).view(b, c)
        
        avg_weight = self.fc(avg_out).view(b, c, 1, 1, 1)
        max_weight = self.fc(max_out).view(b, c, 1, 1, 1)
        
        attention = avg_weight + max_weight
        return x * attention

class EnhancedConvLSTMCell(nn.Module):
    """增强的ConvLSTM单元，集成注意力机制"""
    
    def __init__(self, input_dim: int, hidden_dim: int, kernel_size: int, 
                 bias: bool = True, use_attention: bool = True):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size // 2
        self.bias = bias
        self.use_attention = use_attention
        
        # 主要的卷积层
        self.conv = nn.Conv2d(
            in_channels=self.input_dim + self.hidden_dim,
            out_channels=4 * self.hidden_dim,
            kernel_size=self.kernel_size,
            padding=self.padding,
            bias=self.bias
        )
        
        # 注意力机制
        if self.use_attention:
            self.spatial_attention = SpatialAttention(self.hidden_dim)
        
        # 层归一化
        self.layer_norm = nn.GroupNorm(4, 4 * self.hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout2d(0.1)
    
    def forward(self, input_tensor, cur_state):
        h_cur, c_cur = cur_state
        
        # 连接输入和隐藏状态
        combined = torch.cat([input_tensor, h_cur], dim=1)
        
        # 卷积操作
        combined_conv = self.conv(combined)
        
        # 层归一化
        combined_conv = self.layer_norm(combined_conv)
        
        # 分离门控值
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)
        
        # 应用激活函数
        i = torch.sigmoid(cc_i)
        f = torch.sigmoid(cc_f)
        o = torch.sigmoid(cc_o)
        g = torch.tanh(cc_g)
        
        # 更新单元状态
        c_next = f * c_cur + i * g
        
        # 计算隐藏状态
        h_next = o * torch.tanh(c_next)
        
        # 应用空间注意力
        if self.use_attention:
            h_next = self.spatial_attention(h_next)
        
        # Dropout
        h_next = self.dropout(h_next)
        
        return h_next, c_next

class MultiScaleConvLSTM(nn.Module):
    """多尺度ConvLSTM，支持不同IMF的不同时间尺度"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], kernel_size: int,
                 num_layers: int, imf_seq_lens: List[int], batch_first: bool = True):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dims = hidden_dims
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.imf_seq_lens = imf_seq_lens
        self.batch_first = batch_first
        
        # 为每个IMF创建独立的ConvLSTM分支
        self.imf_branches = nn.ModuleList()
        for i, seq_len in enumerate(imf_seq_lens):
            branch = nn.ModuleList()
            for layer_idx in range(num_layers):
                cur_input_dim = 1 if layer_idx == 0 else hidden_dims[layer_idx-1]
                branch.append(
                    EnhancedConvLSTMCell(
                        input_dim=cur_input_dim,
                        hidden_dim=hidden_dims[layer_idx],
                        kernel_size=kernel_size,
                        use_attention=True
                    )
                )
            self.imf_branches.append(branch)
        
        # IMF融合层
        self.imf_fusion = nn.Conv2d(
            in_channels=len(imf_seq_lens) * hidden_dims[-1],
            out_channels=hidden_dims[-1],
            kernel_size=1
        )
        
        # 通道注意力
        self.channel_attention = ChannelAttention(len(imf_seq_lens))
    
    def _init_hidden(self, batch_size: int, height: int, width: int, device):
        """初始化隐藏状态"""
        hidden_states = []
        for branch in self.imf_branches:
            branch_states = []
            for cell in branch:
                h = torch.zeros(batch_size, cell.hidden_dim, height, width, device=device)
                c = torch.zeros(batch_size, cell.hidden_dim, height, width, device=device)
                branch_states.append((h, c))
            hidden_states.append(branch_states)
        return hidden_states
    
    def forward(self, input_tensor):
        """
        前向传播
        input_tensor: [batch, num_imfs, max_seq_len, height, width]
        """
        batch_size, num_imfs, max_seq_len, height, width = input_tensor.size()
        device = input_tensor.device
        
        # 初始化隐藏状态
        hidden_states = self._init_hidden(batch_size, height, width, device)
        
        # 处理每个IMF分支
        imf_outputs = []
        
        for imf_idx, (seq_len, branch) in enumerate(zip(self.imf_seq_lens, self.imf_branches)):
            # 获取当前IMF的输入序列
            imf_input = input_tensor[:, imf_idx, -seq_len:, :, :]  # 取最后seq_len个时间步
            
            # 通过ConvLSTM层
            cur_input = imf_input
            for layer_idx, cell in enumerate(branch):
                layer_output = []
                h, c = hidden_states[imf_idx][layer_idx]
                
                for t in range(seq_len):
                    h, c = cell(cur_input[:, t], (h, c))
                    layer_output.append(h)
                
                # 更新隐藏状态
                hidden_states[imf_idx][layer_idx] = (h, c)
                
                # 准备下一层的输入
                if layer_idx < len(branch) - 1:
                    cur_input = torch.stack(layer_output, dim=1)
            
            # 取最后一个时间步的输出
            imf_outputs.append(h)
        
        # 堆叠所有IMF的输出
        stacked_outputs = torch.stack(imf_outputs, dim=1)  # [batch, num_imfs, hidden_dim, h, w]
        
        # 应用通道注意力
        attended_outputs = self.channel_attention(stacked_outputs)
        
        # 融合所有IMF的特征
        flattened = attended_outputs.view(batch_size, -1, height, width)
        fused_output = self.imf_fusion(flattened)
        
        return fused_output, imf_outputs

class PhysicsConstrainedLayer(nn.Module):
    """物理约束层，确保预测结果符合物理规律"""
    
    def __init__(self, num_imfs: int, temp_range: Tuple[float, float] = (-2.0, 35.0)):
        super().__init__()
        self.num_imfs = num_imfs
        self.temp_min, self.temp_max = temp_range
        
        # 可学习的约束参数
        self.constraint_weight = nn.Parameter(torch.tensor(0.1))
        
        # 空间平滑卷积
        self.smooth_conv = nn.Conv2d(num_imfs, num_imfs, kernel_size=3, 
                                   padding=1, groups=num_imfs, bias=False)
        
        # 初始化为平均滤波器
        with torch.no_grad():
            self.smooth_conv.weight.fill_(1.0 / 9.0)
    
    def forward(self, imf_predictions):
        """
        应用物理约束
        imf_predictions: [batch, num_imfs, height, width]
        """
        # 重构总SST
        total_sst = torch.sum(imf_predictions, dim=1, keepdim=True)
        
        # 温度范围约束
        constrained_sst = torch.clamp(total_sst, self.temp_min, self.temp_max)
        
        # 空间平滑约束
        smoothed_imfs = self.smooth_conv(imf_predictions)
        
        # 结合原始预测和约束
        constrained_imfs = (1 - self.constraint_weight) * imf_predictions + \
                          self.constraint_weight * smoothed_imfs
        
        # 确保重构的SST在合理范围内
        constrained_total = torch.sum(constrained_imfs, dim=1, keepdim=True)
        if not torch.allclose(constrained_total, constrained_sst, atol=1e-3):
            # 按比例调整IMF以满足总和约束
            scale_factor = constrained_sst / (constrained_total + 1e-8)
            constrained_imfs = constrained_imfs * scale_factor
        
        return constrained_imfs

class NextGenSSTModel(nn.Module):
    """下一代SST预测模型"""
    
    def __init__(self, num_imfs: int, hidden_dims: List[int] = [128, 256, 512],
                 kernel_size: int = 3, imf_seq_lens: List[int] = None,
                 dropout: float = 0.2, use_physics_constraint: bool = True):
        super().__init__()
        
        self.num_imfs = num_imfs
        self.imf_seq_lens = imf_seq_lens or [14] * num_imfs
        self.use_physics_constraint = use_physics_constraint
        
        # 多尺度ConvLSTM主干
        self.backbone = MultiScaleConvLSTM(
            input_dim=1,  # 每个IMF作为单通道输入
            hidden_dims=hidden_dims,
            kernel_size=kernel_size,
            num_layers=len(hidden_dims),
            imf_seq_lens=self.imf_seq_lens
        )
        
        # 特征增强模块
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(hidden_dims[-1], hidden_dims[-1], kernel_size=3, padding=1),
            nn.GroupNorm(8, hidden_dims[-1]),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dims[-1], hidden_dims[-1], kernel_size=3, padding=1),
            nn.GroupNorm(8, hidden_dims[-1]),
            nn.ReLU(inplace=True)
        )
        
        # 多尺度特征提取
        self.multi_scale_convs = nn.ModuleList([
            nn.Conv2d(hidden_dims[-1], 64, kernel_size=k, padding=k//2)
            for k in [1, 3, 5, 7]
        ])
        
        # 特征融合
        self.feature_fusion = nn.Conv2d(64 * 4, hidden_dims[-1], kernel_size=1)
        
        # 输出层
        self.output_layers = nn.ModuleList([
            nn.Conv2d(hidden_dims[-1], 1, kernel_size=1) for _ in range(num_imfs)
        ])
        
        # 物理约束层
        if self.use_physics_constraint:
            self.physics_constraint = PhysicsConstrainedLayer(num_imfs)
        
        # Dropout
        self.dropout = nn.Dropout2d(dropout)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.GroupNorm, nn.BatchNorm2d)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        前向传播
        x: [batch, num_imfs, max_seq_len, height, width]
        """
        # 通过多尺度ConvLSTM主干
        fused_features, imf_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(fused_features)
        
        # 多尺度特征提取
        multi_scale_features = []
        for conv in self.multi_scale_convs:
            multi_scale_features.append(conv(enhanced_features))
        
        # 特征融合
        concatenated = torch.cat(multi_scale_features, dim=1)
        fused = self.feature_fusion(concatenated)
        
        # Dropout
        fused = self.dropout(fused)
        
        # 为每个IMF生成预测
        imf_predictions = []
        for i, output_layer in enumerate(self.output_layers):
            imf_pred = output_layer(fused).squeeze(1)  # 移除通道维度
            imf_predictions.append(imf_pred)
        
        # 堆叠IMF预测
        stacked_predictions = torch.stack(imf_predictions, dim=1)
        
        # 应用物理约束
        if self.use_physics_constraint:
            constrained_predictions = self.physics_constraint(stacked_predictions)
            return constrained_predictions
        
        return stacked_predictions

class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, mse_weight: float = 0.6, l1_weight: float = 0.3, 
                 physics_weight: float = 0.1, imf_weights: Optional[List[float]] = None):
        super().__init__()
        self.mse_weight = mse_weight
        self.l1_weight = l1_weight
        self.physics_weight = physics_weight
        self.imf_weights = imf_weights
        
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()
    
    def forward(self, predictions, targets):
        """
        计算组合损失
        predictions, targets: [batch, num_imfs, height, width]
        """
        # 基础损失
        mse = self.mse_loss(predictions, targets)
        l1 = self.l1_loss(predictions, targets)
        
        # IMF加权损失
        if self.imf_weights is not None:
            weighted_mse = 0
            weighted_l1 = 0
            for i, weight in enumerate(self.imf_weights):
                weighted_mse += weight * self.mse_loss(predictions[:, i], targets[:, i])
                weighted_l1 += weight * self.l1_loss(predictions[:, i], targets[:, i])
            mse = weighted_mse / len(self.imf_weights)
            l1 = weighted_l1 / len(self.imf_weights)
        
        # 物理一致性损失
        pred_total = torch.sum(predictions, dim=1)
        target_total = torch.sum(targets, dim=1)
        physics_loss = self.mse_loss(pred_total, target_total)
        
        # 组合损失
        total_loss = (self.mse_weight * mse + 
                     self.l1_weight * l1 + 
                     self.physics_weight * physics_loss)
        
        return total_loss, {
            'mse': mse.item(),
            'l1': l1.item(),
            'physics': physics_loss.item(),
            'total': total_loss.item()
        }

if __name__ == "__main__":
    # 测试模型
    print("=== 测试下一代SST模型 ===")
    
    # 创建模型
    model = NextGenSSTModel(
        num_imfs=8,
        hidden_dims=[128, 256, 512],
        imf_seq_lens=[7, 7, 14, 14, 21, 21, 30, 30],  # 不同IMF使用不同序列长度
        use_physics_constraint=True
    )
    
    # 测试输入
    batch_size = 4
    num_imfs = 8
    max_seq_len = 30
    height, width = 100, 140
    
    test_input = torch.randn(batch_size, num_imfs, max_seq_len, height, width)
    
    # 前向传播
    with torch.no_grad():
        output = model(test_input)
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {output.shape}")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试损失函数
    criterion = CombinedLoss(imf_weights=[1.0, 1.0, 0.8, 0.8, 0.6, 0.6, 0.4, 0.4])
    target = torch.randn_like(output)
    loss, loss_dict = criterion(output, target)
    print(f"损失值: {loss_dict}")
