"""
高级训练器
支持多种优化策略和自适应训练的训练系统
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, ReduceLROnPlateau
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Tuple
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 15, min_delta: float = 1e-6, 
                 restore_best_weights: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss: float, model: nn.Module) -> bool:
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False

class GradientClipper:
    """梯度裁剪器"""
    
    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        self.max_norm = max_norm
        self.norm_type = norm_type
        
    def __call__(self, model: nn.Module) -> float:
        return torch.nn.utils.clip_grad_norm_(model.parameters(), 
                                            self.max_norm, self.norm_type)

class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        
    def update(self, **kwargs):
        for key, value in kwargs.items():
            self.metrics[key].append(value)
    
    def get_latest(self, key: str) -> float:
        return self.metrics[key][-1] if self.metrics[key] else 0.0
    
    def get_average(self, key: str, last_n: int = None) -> float:
        values = self.metrics[key]
        if not values:
            return 0.0
        if last_n:
            values = values[-last_n:]
        return sum(values) / len(values)
    
    def save(self, filepath: str):
        with open(filepath, 'w') as f:
            json.dump(dict(self.metrics), f, indent=2)
    
    def load(self, filepath: str):
        with open(filepath, 'r') as f:
            loaded_metrics = json.load(f)
            for key, values in loaded_metrics.items():
                self.metrics[key] = values

class AdvancedTrainer:
    """高级训练器"""
    
    def __init__(self, model: nn.Module, criterion: nn.Module, 
                 optimizer: optim.Optimizer, device: torch.device,
                 scheduler: Optional[object] = None,
                 gradient_clipper: Optional[GradientClipper] = None,
                 early_stopping: Optional[EarlyStopping] = None,
                 checkpoint_dir: str = 'checkpoints_v2',
                 log_interval: int = 10,
                 save_interval: int = 5):
        
        self.model = model
        self.criterion = criterion
        self.optimizer = optimizer
        self.device = device
        self.scheduler = scheduler
        self.gradient_clipper = gradient_clipper
        self.early_stopping = early_stopping
        self.checkpoint_dir = checkpoint_dir
        self.log_interval = log_interval
        self.save_interval = save_interval
        
        # 创建检查点目录
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 指标跟踪
        self.metrics_tracker = MetricsTracker()
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.training_start_time = None
        
    def train_epoch(self, train_loader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        epoch_metrics = defaultdict(float)
        num_batches = len(train_loader)
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {self.current_epoch+1} [Train]")
        
        for batch_idx, (inputs, targets) in enumerate(progress_bar):
            # 数据移到设备
            inputs = inputs.to(self.device, non_blocking=True)
            targets = targets.to(self.device, non_blocking=True)
            
            # 清零梯度
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(inputs)
            
            # 计算损失
            if hasattr(self.criterion, 'forward'):
                loss, loss_dict = self.criterion(outputs, targets[:, :, 0])
                total_loss = loss
                for key, value in loss_dict.items():
                    epoch_metrics[f'train_{key}'] += value
            else:
                total_loss = self.criterion(outputs, targets[:, :, 0])
                epoch_metrics['train_total'] += total_loss.item()
            
            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            if self.gradient_clipper:
                grad_norm = self.gradient_clipper(self.model)
                epoch_metrics['grad_norm'] += grad_norm
            
            # 优化器步进
            self.optimizer.step()
            
            # 更新进度条
            if batch_idx % self.log_interval == 0:
                progress_bar.set_postfix({
                    'loss': f"{total_loss.item():.4f}",
                    'lr': f"{self.optimizer.param_groups[0]['lr']:.2e}"
                })
        
        # 计算平均指标
        for key in epoch_metrics:
            epoch_metrics[key] /= num_batches
            
        return dict(epoch_metrics)
    
    def validate_epoch(self, val_loader) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        epoch_metrics = defaultdict(float)
        num_batches = len(val_loader)
        
        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f"Epoch {self.current_epoch+1} [Val]")
            
            for inputs, targets in progress_bar:
                # 数据移到设备
                inputs = inputs.to(self.device, non_blocking=True)
                targets = targets.to(self.device, non_blocking=True)
                
                # 前向传播
                outputs = self.model(inputs)
                
                # 计算损失
                if hasattr(self.criterion, 'forward'):
                    loss, loss_dict = self.criterion(outputs, targets[:, :, 0])
                    for key, value in loss_dict.items():
                        epoch_metrics[f'val_{key}'] += value
                else:
                    loss = self.criterion(outputs, targets[:, :, 0])
                    epoch_metrics['val_total'] += loss.item()
                
                # 计算额外指标
                mae = torch.mean(torch.abs(outputs - targets[:, :, 0]))
                rmse = torch.sqrt(torch.mean((outputs - targets[:, :, 0]) ** 2))
                
                epoch_metrics['val_mae'] += mae.item()
                epoch_metrics['val_rmse'] += rmse.item()
        
        # 计算平均指标
        for key in epoch_metrics:
            epoch_metrics[key] /= num_batches
            
        return dict(epoch_metrics)
    
    def save_checkpoint(self, epoch: int, val_loss: float, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'val_loss': val_loss,
            'best_val_loss': self.best_val_loss,
            'metrics': dict(self.metrics_tracker.metrics)
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 保存常规检查点
        checkpoint_path = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"新的最佳模型已保存: val_loss = {val_loss:.6f}")
    
    def load_checkpoint(self, checkpoint_path: str) -> int:
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.best_val_loss = checkpoint['best_val_loss']
        
        if self.scheduler and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        if 'metrics' in checkpoint:
            for key, values in checkpoint['metrics'].items():
                self.metrics_tracker.metrics[key] = values
        
        print(f"检查点已加载: epoch {checkpoint['epoch']}, val_loss = {checkpoint['val_loss']:.6f}")
        return checkpoint['epoch']
    
    def train(self, train_loader, val_loader, num_epochs: int, 
              resume_from: Optional[str] = None) -> Dict[str, List[float]]:
        """完整训练流程"""
        
        # 恢复训练（如果指定）
        start_epoch = 0
        if resume_from and os.path.exists(resume_from):
            start_epoch = self.load_checkpoint(resume_from) + 1
            print(f"从epoch {start_epoch}恢复训练")
        
        self.training_start_time = time.time()
        
        print(f"开始训练，共 {num_epochs} 个epoch")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"训练设备: {self.device}")
        
        for epoch in range(start_epoch, num_epochs):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # 训练阶段
            train_metrics = self.train_epoch(train_loader)
            
            # 验证阶段
            val_metrics = self.validate_epoch(val_loader)
            
            # 合并指标
            all_metrics = {**train_metrics, **val_metrics}
            
            # 更新指标跟踪器
            self.metrics_tracker.update(**all_metrics)
            
            # 学习率调度
            if self.scheduler:
                if isinstance(self.scheduler, ReduceLROnPlateau):
                    self.scheduler.step(val_metrics.get('val_total', val_metrics.get('val_mse', 0)))
                else:
                    self.scheduler.step()
            
            # 检查是否为最佳模型
            current_val_loss = val_metrics.get('val_total', val_metrics.get('val_mse', float('inf')))
            is_best = current_val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = current_val_loss
            
            # 保存检查点
            if (epoch + 1) % self.save_interval == 0 or is_best:
                self.save_checkpoint(epoch, current_val_loss, is_best)
            
            # 早停检查
            if self.early_stopping:
                if self.early_stopping(current_val_loss, self.model):
                    print(f"早停触发，在epoch {epoch+1}停止训练")
                    break
            
            # 打印epoch总结
            epoch_time = time.time() - epoch_start_time
            print(f"\nEpoch {epoch+1}/{num_epochs} 完成 ({epoch_time:.1f}s)")
            print(f"训练损失: {train_metrics.get('train_total', train_metrics.get('train_mse', 0)):.6f}")
            print(f"验证损失: {current_val_loss:.6f}")
            print(f"验证MAE: {val_metrics.get('val_mae', 0):.6f}")
            print(f"验证RMSE: {val_metrics.get('val_rmse', 0):.6f}")
            print(f"学习率: {self.optimizer.param_groups[0]['lr']:.2e}")
            print("-" * 50)
        
        # 保存最终指标
        metrics_path = os.path.join(self.checkpoint_dir, 'training_metrics.json')
        self.metrics_tracker.save(metrics_path)
        
        total_time = time.time() - self.training_start_time
        print(f"\n训练完成！总用时: {total_time/3600:.2f} 小时")
        print(f"最佳验证损失: {self.best_val_loss:.6f}")
        
        return dict(self.metrics_tracker.metrics)

def create_advanced_trainer(model: nn.Module, criterion: nn.Module, 
                          device: torch.device, learning_rate: float = 1e-4,
                          weight_decay: float = 1e-4, scheduler_type: str = 'cosine',
                          max_grad_norm: float = 1.0, patience: int = 15) -> AdvancedTrainer:
    """创建高级训练器"""
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, 
                           weight_decay=weight_decay, betas=(0.9, 0.999))
    
    # 学习率调度器
    if scheduler_type == 'cosine':
        scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, eta_min=1e-7)
    elif scheduler_type == 'plateau':
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, 
                                    patience=5, verbose=True, min_lr=1e-7)
    else:
        scheduler = None
    
    # 梯度裁剪器
    gradient_clipper = GradientClipper(max_norm=max_grad_norm)
    
    # 早停机制
    early_stopping = EarlyStopping(patience=patience, min_delta=1e-6)
    
    # 创建训练器
    trainer = AdvancedTrainer(
        model=model,
        criterion=criterion,
        optimizer=optimizer,
        device=device,
        scheduler=scheduler,
        gradient_clipper=gradient_clipper,
        early_stopping=early_stopping,
        checkpoint_dir='checkpoints_v2'
    )
    
    return trainer

def plot_training_history(metrics: Dict[str, List[float]], save_path: str = None):
    """绘制训练历史"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    if 'train_total' in metrics and 'val_total' in metrics:
        axes[0, 0].plot(metrics['train_total'], label='训练损失')
        axes[0, 0].plot(metrics['val_total'], label='验证损失')
        axes[0, 0].set_title('损失曲线')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
    
    # MAE曲线
    if 'val_mae' in metrics:
        axes[0, 1].plot(metrics['val_mae'], label='验证MAE', color='orange')
        axes[0, 1].set_title('MAE曲线')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('MAE')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    
    # RMSE曲线
    if 'val_rmse' in metrics:
        axes[1, 0].plot(metrics['val_rmse'], label='验证RMSE', color='red')
        axes[1, 0].set_title('RMSE曲线')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('RMSE')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
    
    # 梯度范数
    if 'grad_norm' in metrics:
        axes[1, 1].plot(metrics['grad_norm'], label='梯度范数', color='green')
        axes[1, 1].set_title('梯度范数')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Gradient Norm')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练历史图已保存到: {save_path}")
    else:
        plt.show()

if __name__ == "__main__":
    # 示例使用
    print("=== 高级训练器测试 ===")
    
    # 这里可以添加测试代码
    pass
