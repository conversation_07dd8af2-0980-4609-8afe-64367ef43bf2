# MEEMD-ConvLSTM v2.0 依赖包
# 基于分解-预测-重构思路的下一代海表温度预测系统

# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# 数据处理和科学计算
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0
xarray>=0.19.0
netCDF4>=1.5.7

# 信号处理和MEEMD
PyEMD>=0.3.3
emd>=0.5.0

# 机器学习和统计
scikit-learn>=1.0.0
statsmodels>=0.13.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
cartopy>=0.20.0

# 图像处理
Pillow>=8.3.0
opencv-python>=4.5.0

# 进度条和工具
tqdm>=4.62.0
click>=8.0.0

# 配置和日志
pyyaml>=6.0
configparser>=5.2.0
loguru>=0.6.0

# 并行处理
joblib>=1.1.0
multiprocessing-logging>=0.3.0

# 内存优化
psutil>=5.8.0
memory-profiler>=0.60.0

# 数据验证
cerberus>=1.3.0
marshmallow>=3.14.0

# 实验跟踪（可选）
wandb>=0.12.0
tensorboard>=2.8.0
mlflow>=1.20.0

# 开发和测试工具
pytest>=6.2.0
pytest-cov>=3.0.0
black>=21.9.0
flake8>=4.0.0
mypy>=0.910

# Jupyter支持
jupyter>=1.0.0
ipywidgets>=7.6.0
notebook>=6.4.0

# 性能分析
line-profiler>=3.3.0
py-spy>=0.3.0

# 文档生成
sphinx>=4.2.0
sphinx-rtd-theme>=1.0.0

# 其他实用工具
rich>=10.12.0
typer>=0.4.0
pathlib2>=2.3.0
