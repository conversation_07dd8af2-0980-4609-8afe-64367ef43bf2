<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Dataset数据流程图" id="dataset-data-flow">
    <mxGraphModel dx="2066" dy="1137" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="0" fold="1" page="1" pageScale="1" pageWidth="2339" pageHeight="3300" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1100" y="50" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="init_dataset" value="SSTDataset.__init__()&#xa;初始化数据集类" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1100" y="150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="set_params" value="设置参数&#xa;input_seq_len=14&#xa;pred_seq_len=1&#xa;subset=&#39;train&#39;/&#39;val&#39;/&#39;test&#39;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1040" y="245" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="load_metadata" value="加载元数据&#xa;meemd_metadata.npy&#xa;获取num_imfs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="50" y="250" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="dataset_info_title" value="数据集信息加载模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="38" y="170" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="load_first_imf" value="加载第一个IMF文件&#xa;imf_1.nc&#xa;获取基本信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="50" y="382" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="time_processing" value="时间维度处理&#xa;total_times = len(time_values)&#xa;根据subset划分时间范围" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="50" y="506" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="data_split_detail" value="数据集划分详情&#xa;train: [0, 80%)&#xa;val: [80%, 90%)&#xa;test: [90%, 100%]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="280" y="530" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="spatial_dims" value="获取空间维度&#xa;lats = ds.latitude.values&#xa;lons = ds.longitude.values&#xa;spatial_shape = (lat, lon)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="50" y="628" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="calc_samples" value="计算样本数量&#xa;num_samples = &#xa;len(time_values) - &#xa;input_seq_len - pred_seq_len + 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="50" y="764" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="data_structure_title" value="三维数据结构" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1629" y="257" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="original_imf_structure" value="原始IMF数据结构&#xa;[time, latitude, longitude]&#xa;例: [365, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;rotation=0;" parent="1" vertex="1">
          <mxGeometry x="1657.5" y="339" width="143" height="72" as="geometry" />
        </mxCell>
        <mxCell id="cache_structure" value="缓存数据结构&#xa;imf_data:&#xa;[num_imfs, time_steps, lat, lon]&#xa;例: [6, 292, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;rotation=0;" parent="1" vertex="1">
          <mxGeometry x="1657.5" y="458" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sliding_window_structure" value="滑动窗口数据结构&#xa;input_data:&#xa;[num_imfs, input_seq_len, lat, lon]&#xa;例: [6, 14, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;rotation=0;" parent="1" vertex="1">
          <mxGeometry x="1655" y="595" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="target_structure" value="目标数据结构&#xa;target_data:&#xa;[num_imfs, pred_seq_len, lat, lon]&#xa;例: [6, 1, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;rotation=0;" parent="1" vertex="1">
          <mxGeometry x="1657.5" y="729" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="cache_check" value="是否缓存数据?&#xa;cache_data" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="800" y="430" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cache_all_imfs" value="缓存所有IMF组件&#xa;_cache_all_imfs()&#xa;加载到内存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="780" y="550" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="data_loading_title" value="数据加载模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1000" y="360" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="getitem_method" value="__getitem__(idx)&#xa;获取单个样本" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1020" y="430" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="time_indices" value="计算时间索引&#xa;input_indices = [idx:idx+14]&#xa;target_indices = [idx+14:idx+15]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1020" y="520" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="load_time_slice" value="_load_time_slice()&#xa;加载指定时间片数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1020" y="630" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="data_conversion" value="数据转换&#xa;numpy → PyTorch tensor&#xa;torch.from_numpy().float()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1020" y="720" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="dataloader_title" value="DataLoader创建模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1300" y="360" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="create_datasets" value="创建三个数据集&#xa;train_dataset&#xa;val_dataset&#xa;test_dataset" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1320" y="430" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="create_dataloaders" value="创建DataLoader&#xa;batch_size=4&#xa;shuffle=True/False&#xa;num_workers=4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1320" y="540" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="batch_structure" value="批处理数据结构&#xa;batch_input:&#xa;[batch_size, num_imfs, seq_len, lat, lon]&#xa;例: [4, 6, 14, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11;rotation=5" parent="1" vertex="1">
          <mxGeometry x="1320" y="650" width="160" height="100" as="geometry" />
        </mxCell>
        <mxCell id="end" value="数据准备完成" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1100" y="900" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="start" target="init_dataset" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="init_dataset" target="set_params" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="set_params" target="load_metadata" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="load_metadata" target="load_first_imf" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="load_first_imf" target="time_processing" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="time_processing" target="data_split_detail" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="time_processing" target="spatial_dims" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="spatial_dims" target="calc_samples" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="calc_samples" target="cache_check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="cache_check" target="cache_all_imfs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="cache_check" target="getitem_method" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="getitem_method" target="time_indices" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="time_indices" target="load_time_slice" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="load_time_slice" target="data_conversion" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="data_conversion" target="create_datasets" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="create_datasets" target="create_dataloaders" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="create_dataloaders" target="batch_structure" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="batch_structure" target="end" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label_yes" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="860" y="530" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="label_no" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="950" y="450" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="3d_visualization_title" value="三维数据结构可视化" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="1000" width="1400" height="50" as="geometry" />
        </mxCell>
        <mxCell id="netcdf_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#dae8fc;strokeColor=#6c8ebf;size=20" parent="1" vertex="1">
          <mxGeometry x="100" y="1100" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="netcdf_label" value="原始NetCDF数据&#xa;[time, lat, lon]&#xa;[365, 180, 360]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="100" y="1200" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="imf_4d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ffe6cc;strokeColor=#d79b00;size=25" parent="1" vertex="1">
          <mxGeometry x="300" y="1080" width="140" height="100" as="geometry" />
        </mxCell>
        <mxCell id="imf_label" value="IMF分解数据&#xa;[num_imfs, time, lat, lon]&#xa;[6, 365, 180, 360]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="300" y="1200" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="input_window_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#d5e8d4;strokeColor=#82b366;size=20" parent="1" vertex="1">
          <mxGeometry x="520" y="1100" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="input_label" value="输入窗口数据&#xa;[num_imfs, seq_len, lat, lon]&#xa;[6, 14, 180, 360]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="520" y="1200" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="target_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#fff2cc;strokeColor=#d6b656;size=15" parent="1" vertex="1">
          <mxGeometry x="720" y="1110" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="target_label" value="目标数据&#xa;[num_imfs, pred_len, lat, lon]&#xa;[6, 1, 180, 360]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="720" y="1200" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="batch_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#e1d5e7;strokeColor=#9673a6;size=30" parent="1" vertex="1">
          <mxGeometry x="900" y="1070" width="160" height="110" as="geometry" />
        </mxCell>
        <mxCell id="batch_label" value="批处理数据&#xa;[batch, num_imfs, seq_len, lat, lon]&#xa;[4, 6, 14, 180, 360]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="900" y="1200" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="netcdf_3d" target="imf_4d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="imf_4d" target="input_window_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="input_window_3d" target="target_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="target_3d" target="batch_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="transform1" value="MEEMD分解" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="230" y="1120" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform2" value="滑动窗口" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="450" y="1120" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform3" value="目标提取" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="650" y="1120" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform4" value="批处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="830" y="1120" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="sliding_window_detail_title" value="滑动窗口机制详解" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="1300" width="1400" height="40" as="geometry" />
        </mxCell>
        <mxCell id="timeline" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666" parent="1" vertex="1">
          <mxGeometry x="100" y="1380" width="1200" height="20" as="geometry" />
        </mxCell>
        <mxCell id="timeline_label" value="时间轴 (天)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="100" y="1350" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="input_window_example" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366" parent="1" vertex="1">
          <mxGeometry x="200" y="1380" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="input_window_label" value="输入窗口 (14天)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="200" y="1410" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="target_window_example" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656" parent="1" vertex="1">
          <mxGeometry x="350" y="1380" width="10" height="20" as="geometry" />
        </mxCell>
        <mxCell id="target_window_label" value="目标 (1天)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="320" y="1410" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="next_sample_input" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1" parent="1" vertex="1">
          <mxGeometry x="210" y="1380" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="next_sample_target" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;dashed=1" parent="1" vertex="1">
          <mxGeometry x="360" y="1380" width="10" height="20" as="geometry" />
        </mxCell>
        <mxCell id="next_sample_label" value="下一个样本 (滑动1天)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="210" y="1440" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="time_scale" value="0    1    2    3    ...    13   14   15   ...   364" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9" parent="1" vertex="1">
          <mxGeometry x="100" y="1470" width="400" height="20" as="geometry" />
        </mxCell>
        <mxCell id="dimension_flow_title" value="数据维度变化流程" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="1520" width="1400" height="40" as="geometry" />
        </mxCell>
        <mxCell id="dim_step1" value="步骤1: 加载单个IMF&#xa;[time, lat, lon]&#xa;[365, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="100" y="1580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dim_step2" value="步骤2: 堆叠所有IMF&#xa;[num_imfs, time, lat, lon]&#xa;[6, 365, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="280" y="1580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dim_step3" value="步骤3: 时间切片&#xa;[num_imfs, seq_len, lat, lon]&#xa;[6, 14, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="460" y="1580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dim_step4" value="步骤4: 转换为Tensor&#xa;torch.FloatTensor&#xa;[6, 14, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="640" y="1580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dim_step5" value="步骤5: 批处理组合&#xa;[batch, num_imfs, seq_len, lat, lon]&#xa;[4, 6, 14, 180, 360]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="820" y="1580" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dim_arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="dim_step1" target="dim_step2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dim_arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="dim_step2" target="dim_step3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dim_arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="dim_step3" target="dim_step4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dim_arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;" parent="1" source="dim_step4" target="dim_step5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="memory_usage_title" value="内存使用分析" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1150" y="1580" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="memory_cache" value="缓存模式:&#xa;全部加载到内存&#xa;内存占用: ~15GB&#xa;(6×365×180×360×4字节)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1100" y="1640" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="memory_disk" value="磁盘模式:&#xa;按需加载&#xa;内存占用: ~60MB&#xa;(6×14×180×360×4字节)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1260" y="1640" width="140" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
