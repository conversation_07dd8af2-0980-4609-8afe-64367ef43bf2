"""
高级数据集模块
支持多尺度时间窗口和智能缓存的数据加载器
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import xarray as xr
from typing import List, Tuple, Optional, Dict
import random
from collections import defaultdict

class MultiScaleSSTDataset(Dataset):
    """
    多尺度SST数据集
    支持不同时间尺度的输入窗口，适应不同IMF分量的特征
    """
    
    def __init__(self, imf_dir: str, input_seq_lens: List[int] = [7, 14, 30], 
                 pred_seq_len: int = 1, subset: str = 'train',
                 cache_data: bool = False, augmentation: bool = True,
                 imf_weights: Optional[List[float]] = None):
        """
        初始化多尺度数据集
        
        参数:
            imf_dir: IMF组件目录
            input_seq_lens: 不同IMF使用的输入序列长度列表
            pred_seq_len: 预测序列长度
            subset: 数据集子集 ('train', 'val', 'test')
            cache_data: 是否缓存数据到内存
            augmentation: 是否使用数据增强
            imf_weights: IMF分量权重，用于加权损失
        """
        self.imf_dir = os.path.abspath(imf_dir)
        self.input_seq_lens = input_seq_lens
        self.pred_seq_len = pred_seq_len
        self.subset = subset
        self.cache_data = cache_data
        self.augmentation = augmentation and subset == 'train'
        
        # 加载元数据
        self._load_metadata()
        
        # 设置IMF权重
        self.imf_weights = imf_weights or [1.0] * self.num_imfs
        
        # 加载数据集信息
        self._load_dataset_info()
        
        # 智能分配序列长度
        self._assign_sequence_lengths()
        
        # 缓存数据（如果需要）
        if self.cache_data:
            self._cache_all_imfs()
        
        # 数据增强器
        if self.augmentation:
            self.augmenter = SSTDataAugmentation()
    
    def _load_metadata(self):
        """加载MEEMD元数据"""
        metadata_path = os.path.join(os.path.dirname(self.imf_dir), 'meemd_metadata_v2.npy')
        if not os.path.exists(metadata_path):
            metadata_path = os.path.join(os.path.dirname(self.imf_dir), 'meemd_metadata.npy')
        
        self.metadata = np.load(metadata_path, allow_pickle=True).item()
        self.num_imfs = self.metadata['num_imfs']
        print(f"加载元数据: {self.num_imfs} 个IMF分量")
    
    def _load_dataset_info(self):
        """加载数据集基本信息"""
        # 加载第一个IMF文件获取基本信息
        first_imf_path = os.path.join(self.imf_dir, 'imf_1.nc')
        ds = xr.open_dataset(first_imf_path)
        
        self.lats = ds.latitude.values
        self.lons = ds.longitude.values
        self.spatial_shape = (len(self.lats), len(self.lons))
        
        # 时间维度处理
        total_times = len(ds.time.values)
        
        # 数据集划分
        train_end = int(total_times * 0.8)
        val_end = int(total_times * 0.9)
        
        if self.subset == 'train':
            self.time_start, self.time_end = 0, train_end
        elif self.subset == 'val':
            self.time_start, self.time_end = train_end, val_end
        else:  # test
            self.time_start, self.time_end = val_end, total_times
        
        self.time_values = ds.time.values[self.time_start:self.time_end]
        ds.close()
        
        print(f"{self.subset}数据集: 时间步数 {len(self.time_values)}, 空间形状 {self.spatial_shape}")
    
    def _assign_sequence_lengths(self):
        """智能分配序列长度给不同IMF"""
        if len(self.input_seq_lens) == 1:
            # 如果只有一个序列长度，所有IMF使用相同长度
            self.imf_seq_lens = [self.input_seq_lens[0]] * self.num_imfs
        elif len(self.input_seq_lens) == self.num_imfs:
            # 如果序列长度数量等于IMF数量，一一对应
            self.imf_seq_lens = self.input_seq_lens
        else:
            # 智能分配：高频IMF使用短序列，低频IMF使用长序列
            self.imf_seq_lens = []
            for i in range(self.num_imfs):
                if i < len(self.input_seq_lens):
                    self.imf_seq_lens.append(self.input_seq_lens[i])
                else:
                    # 为剩余IMF分配最长的序列长度
                    self.imf_seq_lens.append(max(self.input_seq_lens))
        
        # 计算最大序列长度，用于确定样本数量
        self.max_seq_len = max(self.imf_seq_lens)
        
        # 计算有效样本数量
        self.num_samples = len(self.time_values) - self.max_seq_len - self.pred_seq_len + 1
        
        print(f"IMF序列长度分配: {self.imf_seq_lens}")
        print(f"最大序列长度: {self.max_seq_len}, 样本数量: {self.num_samples}")
    
    def _cache_all_imfs(self):
        """缓存所有IMF数据到内存"""
        print("缓存IMF数据到内存...")
        self.cached_imfs = {}
        
        for imf_idx in range(self.num_imfs):
            imf_path = os.path.join(self.imf_dir, f'imf_{imf_idx + 1}.nc')
            ds = xr.open_dataset(imf_path)
            
            # 只加载当前子集的时间范围
            imf_data = ds[f'imf_{imf_idx + 1}'].values[self.time_start:self.time_end]
            self.cached_imfs[imf_idx] = imf_data
            ds.close()
        
        print(f"缓存完成，内存占用约 {self._estimate_memory_usage():.1f} GB")
    
    def _estimate_memory_usage(self):
        """估算内存占用"""
        if hasattr(self, 'cached_imfs'):
            total_size = sum(data.nbytes for data in self.cached_imfs.values())
            return total_size / (1024**3)  # 转换为GB
        return 0
    
    def _load_imf_time_slice(self, imf_idx: int, time_indices: List[int]) -> np.ndarray:
        """加载特定IMF的时间片"""
        if self.cache_data:
            return self.cached_imfs[imf_idx][time_indices]
        else:
            imf_path = os.path.join(self.imf_dir, f'imf_{imf_idx + 1}.nc')
            ds = xr.open_dataset(imf_path)
            
            # 调整时间索引到全局时间范围
            global_indices = [self.time_start + idx for idx in time_indices]
            data = ds[f'imf_{imf_idx + 1}'].values[global_indices]
            ds.close()
            return data
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        """获取单个样本"""
        # 为每个IMF准备不同长度的输入序列
        input_data = []
        target_data = []
        
        for imf_idx in range(self.num_imfs):
            seq_len = self.imf_seq_lens[imf_idx]
            
            # 计算输入时间索引
            input_start = self.max_seq_len - seq_len + idx
            input_end = self.max_seq_len + idx
            input_indices = list(range(input_start, input_end))
            
            # 计算目标时间索引
            target_start = self.max_seq_len + idx
            target_end = target_start + self.pred_seq_len
            target_indices = list(range(target_start, target_end))
            
            # 加载数据
            imf_input = self._load_imf_time_slice(imf_idx, input_indices)
            imf_target = self._load_imf_time_slice(imf_idx, target_indices)
            
            input_data.append(imf_input)
            target_data.append(imf_target)
        
        # 转换为numpy数组
        input_array = np.array(input_data)  # [num_imfs, seq_len, lat, lon]
        target_array = np.array(target_data)  # [num_imfs, pred_len, lat, lon]
        
        # 数据增强
        if self.augmentation:
            input_array = self.augmenter(input_array)
        
        # 转换为PyTorch张量
        input_tensor = torch.from_numpy(input_array).float()
        target_tensor = torch.from_numpy(target_array).float()
        
        return input_tensor, target_tensor

class SSTDataAugmentation:
    """SST数据增强器"""
    
    def __init__(self, noise_std=0.01, dropout_prob=0.05, 
                 spatial_shift_range=2, temporal_mask_prob=0.1):
        self.noise_std = noise_std
        self.dropout_prob = dropout_prob
        self.spatial_shift_range = spatial_shift_range
        self.temporal_mask_prob = temporal_mask_prob
    
    def __call__(self, data):
        """应用数据增强"""
        augmented = data.copy()
        
        # 1. 添加高斯噪声
        if random.random() < 0.7:
            noise = np.random.normal(0, self.noise_std, data.shape)
            augmented += noise
        
        # 2. 随机dropout
        if random.random() < 0.3:
            mask = np.random.random(data.shape) > self.dropout_prob
            augmented *= mask
        
        # 3. 时间步掩码
        if random.random() < 0.2:
            for imf_idx in range(data.shape[0]):
                seq_len = data.shape[1]
                if random.random() < self.temporal_mask_prob:
                    mask_idx = random.randint(0, seq_len - 1)
                    augmented[imf_idx, mask_idx] = 0
        
        # 4. 空间微小偏移（通过插值实现）
        if random.random() < 0.1:
            # 简化实现：添加小幅度的空间相关噪声
            spatial_noise = np.random.normal(0, self.noise_std * 0.5, 
                                           (data.shape[0], data.shape[1], 1, 1))
            augmented += spatial_noise
        
        return augmented

def create_advanced_dataloaders(imf_dir: str, batch_size: int = 8,
                              input_seq_lens: List[int] = [7, 14, 30],
                              pred_seq_len: int = 1, num_workers: int = 4,
                              cache_data: bool = False, augmentation: bool = True,
                              imf_weights: Optional[List[float]] = None) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    创建高级数据加载器
    
    返回:
        train_loader, val_loader, test_loader
    """
    print("创建高级数据加载器...")
    
    # 创建数据集
    train_dataset = MultiScaleSSTDataset(
        imf_dir=imf_dir,
        input_seq_lens=input_seq_lens,
        pred_seq_len=pred_seq_len,
        subset='train',
        cache_data=cache_data,
        augmentation=augmentation,
        imf_weights=imf_weights
    )
    
    val_dataset = MultiScaleSSTDataset(
        imf_dir=imf_dir,
        input_seq_lens=input_seq_lens,
        pred_seq_len=pred_seq_len,
        subset='val',
        cache_data=cache_data,
        augmentation=False,  # 验证集不使用增强
        imf_weights=imf_weights
    )
    
    test_dataset = MultiScaleSSTDataset(
        imf_dir=imf_dir,
        input_seq_lens=input_seq_lens,
        pred_seq_len=pred_seq_len,
        subset='test',
        cache_data=cache_data,
        augmentation=False,  # 测试集不使用增强
        imf_weights=imf_weights
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=False
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=False
    )
    
    print(f"数据加载器创建完成:")
    print(f"  训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    print(f"  验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    print(f"  测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    return train_loader, val_loader, test_loader

if __name__ == "__main__":
    # 测试数据加载器
    print("=== 测试高级数据加载器 ===")
    
    train_loader, val_loader, test_loader = create_advanced_dataloaders(
        imf_dir='meemd_results_v2',
        batch_size=4,
        input_seq_lens=[7, 14, 30],  # 不同IMF使用不同序列长度
        cache_data=False,
        augmentation=True
    )
    
    # 测试一个批次
    for batch_input, batch_target in train_loader:
        print(f"批次输入形状: {batch_input.shape}")
        print(f"批次目标形状: {batch_target.shape}")
        break
