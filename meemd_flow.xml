<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="Dataset数据流程图" id="dataset-data-flow">
    <mxGraphModel dx="2066" dy="4437" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="0" fold="1" page="1" pageScale="1" pageWidth="2339" pageHeight="3300" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1100" y="-3" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="main_func" value="main()函数入口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1100" y="150" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="preprocess_title" value="数据预处理模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="250" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="read_netcdf" value="读取NetCDF文件&#xa;xr.open_dataset(file_path)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="320" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check_var_name" value="确认SST变量名&#xa;analysed_sst 或 sst" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="410" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="temp_convert" value="温度单位转换&#xa;Kelvin → Celsius&#xa;sst = sst - 273.15" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="500" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="standardize_data" value="数据标准化&#xa;sst_standardized = &#xa;(sst - mean) / std" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="600" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="data_split" value="数据集划分&#xa;训练集(80%)&#xa;验证集(10%)&#xa;测试集(10%)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="700" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="meemd_title" value="MEEMD分解模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="350" y="250" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="init_meemd2d" value="初始化MEEMD2D类&#xa;ensemble_size=100&#xa;noise_std=0.1&#xa;n_jobs=cpu_count()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="370" y="320" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="reshape_data" value="重组数据为时空矩阵&#xa;(空间点 × 时间点)&#xa;n_spatial_points × n_time" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="370" y="430" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="batch_setup" value="批处理设置&#xa;batch_size=1000&#xa;batch_count计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="370" y="530" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="first_batch_imf" value="处理第一小批数据&#xa;确定IMF数量&#xa;first_batch_size=100" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="370" y="620" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="decompose_title" value="二维MEEMD分解算法" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="650" y="250" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="column_eemd" value="第一次分解(列方向)&#xa;对每列进行EEMD&#xa;perform_1d_eemd()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="320" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="create_g_matrix" value="创建伪IMF矩阵&#xa;g_j(m,n)&#xa;整理列分解结果" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="420" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="row_eemd" value="第二次分解(行方向)&#xa;对每行进行EEMD&#xa;处理g_j矩阵" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="520" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="create_h_matrix" value="创建h_j,k(m,n)矩阵&#xa;整理行分解结果&#xa;确定最大k值" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="620" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="combine_imf" value="根据可比较最小尺度原则&#xa;组合IMF分量&#xa;C_i = Σh_{i,k} + Σh_{j,i}" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="720" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="edge1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="start" target="main_func" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="main_func" target="read_netcdf" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1100" y="100" />
              <mxPoint x="608" y="100" />
              <mxPoint x="608" y="314" />
              <mxPoint x="230" y="314" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="read_netcdf" target="check_var_name" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="check_var_name" target="temp_convert" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="temp_convert" target="standardize_data" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="standardize_data" target="data_split" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="data_split" target="init_meemd2d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="init_meemd2d" target="reshape_data" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="reshape_data" target="batch_setup" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="batch_setup" target="first_batch_imf" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="first_batch_imf" target="column_eemd" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="column_eemd" target="create_g_matrix" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="create_g_matrix" target="row_eemd" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="row_eemd" target="create_h_matrix" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="create_h_matrix" target="combine_imf" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="batch_title" value="批处理循环模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="950" y="250" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="batch_loop" value="批处理循环&#xa;batch_idx &lt; batch_count?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="990" y="320" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="extract_batch" value="提取当前批次&#xa;时间序列数据&#xa;batch_data[i, :]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="970" y="430" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="apply_meemd" value="应用MEEMD分解&#xa;meemd.decompose()&#xa;batch_imfs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="970" y="530" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="store_results" value="存储分解结果&#xa;到对应时空位置&#xa;imf_arrays[imf_idx]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="970" y="630" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="memory_title" value="内存优化管理模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1250" y="250" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="memmap_check" value="使用内存映射?&#xa;use_memmap" style="rhombus;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1290" y="320" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="create_memmap" value="创建内存映射文件&#xa;np.memmap()&#xa;imf_*_memmap.dat" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1270" y="430" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="gc_collect" value="强制垃圾回收&#xa;gc.collect()&#xa;释放内存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1270" y="530" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="save_title" value="结果保存模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="850" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="save_individual_imf" value="分别保存各IMF组件&#xa;imf_ds.to_netcdf()&#xa;imf_1.nc, imf_2.nc..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="920" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="save_metadata" value="保存元数据&#xa;np.save()&#xa;meemd_metadata.npy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="1030" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="merge_dataset" value="合并所有IMF&#xa;创建完整数据集&#xa;spatiotemporal_meemd_decomposition.nc" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="1130" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="viz_title" value="可视化模块" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="940" y="854" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="plot_imfs" value="绘制各IMF分量&#xa;plt.imshow()&#xa;总和图像" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="960" y="941" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="save_plots" value="保存可视化结果&#xa;plt.savefig()&#xa;meemd_decomposition_*.png" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="960" y="1057" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cleanup" value="清理临时文件&#xa;shutil.rmtree()&#xa;删除内存映射文件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="960" y="1245" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="end" value="处理完成" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1298" y="1255" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edge16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="combine_imf" target="batch_loop" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="batch_loop" target="extract_batch" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="extract_batch" target="apply_meemd" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="apply_meemd" target="store_results" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="store_results" target="memmap_check" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="memmap_check" target="create_memmap" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="create_memmap" target="gc_collect" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="gc_collect" target="batch_loop" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1200" y="565" />
              <mxPoint x="1200" y="360" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="batch_loop" target="save_individual_imf" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="360" />
              <mxPoint x="900" y="955" />
              <mxPoint x="230" y="955" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edge25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="save_individual_imf" target="save_metadata" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="save_metadata" target="merge_dataset" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="merge_dataset" target="plot_imfs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="plot_imfs" target="save_plots" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="save_plots" target="cleanup" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="cleanup" target="end" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label_yes1" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1050" y="410" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="label_no1" value="否" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="850" y="340" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="label_yes2" value="是" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1350" y="410" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="label_loop" value="继续循环" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10" parent="1" vertex="1">
          <mxGeometry x="1200" y="340" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="parallel_note" value="支持多进程并行处理&#xa;mp.Pool(n_jobs)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=10;fontStyle=2;dashed=1" parent="1" vertex="1">
          <mxGeometry x="813" y="152" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="formula_note" value="核心公式:&#xa;C_i = Σ(k=i to K) h_{i,k} + Σ(j=i+1 to J) h_{j,i}" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;fontStyle=2;dashed=1" parent="1" vertex="1">
          <mxGeometry x="1195" y="884" width="200" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
