def evaluate_with_original_data(predictions, original_sst, residue=None, num_samples=10):
    """
    随机选取样本，计算预测结果与原始SST数据的评估指标
    
    参数:
        predictions: 预测结果数组 [samples, num_imfs, lat, lon]
        original_sst: 原始SST数据
        residue: 残差项，用于重构完整的SST
        num_samples: 随机选取的样本数量
        
    返回:
        metrics: 评估指标字典
    """
    if original_sst is None:
        print("警告: 无法获取原始SST数据，无法计算评估指标")
        return None
    
    # 确保样本数量不超过可用数据量
    num_samples = min(num_samples, len(predictions), len(original_sst))
    
    # 随机选取样本索引
    np.random.seed(42)  # 固定随机种子以便结果可复现
    sample_indices = np.random.choice(len(predictions), num_samples, replace=False)
    
    print(f"随机选取 {num_samples} 个样本计算评估指标:")
    print(f"样本索引: {sample_indices}")
    
    # 计算所有IMF的总和
    pred_samples = np.sum(predictions[sample_indices], axis=1)  # [num_samples, lat, lon]
    
    # 获取对应的原始SST数据
    true_samples = original_sst[sample_indices]
    
    # 如果有残差项，加入残差以重构完整的预测SST
    if residue is not None:
        if isinstance(residue, (int, float)):
            # 如果残差是标量，直接加到所有位置
            pred_samples += residue
        else:
            # 如果残差是数组，确保形状匹配
            if len(pred_samples.shape) == 3 and len(residue.shape) == 2:
                # 广播残差到所有样本
                pred_samples += residue[np.newaxis, :, :]
            else:
                print(f"警告: 残差形状 {residue.shape} 与数据形状不匹配")
    
    # 计算评估指标
    mae = np.mean(np.abs(pred_samples - true_samples))
    mse = np.mean((pred_samples - true_samples) ** 2)
    rmse = np.sqrt(mse)
    
    # 计算每个样本的评估指标
    sample_metrics = []
    for i in range(num_samples):
        sample_mae = np.mean(np.abs(pred_samples[i] - true_samples[i]))
        sample_mse = np.mean((pred_samples[i] - true_samples[i]) ** 2)
        sample_rmse = np.sqrt(sample_mse)
        sample_metrics.append({
            'index': sample_indices[i],
            'mae': sample_mae,
            'mse': sample_mse,
            'rmse': sample_rmse
        })
    
    # 输出每个样本的评估指标
    print("\n样本评估指标:")
    for metric in sample_metrics:
        print(f"样本 {metric['index']}: MAE={metric['mae']:.6f}, MSE={metric['mse']:.6f}, RMSE={metric['rmse']:.6f}")
    
    # 输出总体评估指标
    print(f"\n总体评估指标 (基于原始SST数据):")
    print(f"MAE: {mae:.6f}, MSE: {mse:.6f}, RMSE: {rmse:.6f}")
    
    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'sample_metrics': sample_metrics
    }
    
    return metrics 