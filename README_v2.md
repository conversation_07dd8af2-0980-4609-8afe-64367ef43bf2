# MEEMD-ConvLSTM v2.0: 下一代海表温度预测系统

基于**分解-预测-重构**思路的先进海表温度预测系统，结合自适应MEEMD分解和多尺度ConvLSTM深度学习架构。

## 🌊 项目概述

本项目实现了一个创新的海表温度(SST)预测框架，核心思想是：
1. **分解**：使用自适应MEEMD将复杂的SST信号分解为多个本征模态函数(IMF)
2. **预测**：针对不同IMF分量使用多尺度ConvLSTM进行独立预测
3. **重构**：将预测的IMF分量重构为最终的SST预测结果

## 🚀 核心创新

### 1. 自适应MEEMD分解
- **自适应噪声标准差**：根据信号特征动态调整
- **质量评估机制**：实时监控分解质量
- **并行处理**：支持大规模时空数据的高效分解
- **边界效应处理**：减少时间序列边界的伪影

### 2. 多尺度ConvLSTM架构
- **IMF特异性设计**：不同IMF使用不同的时间窗口长度
- **注意力机制**：空间注意力和通道注意力的双重增强
- **物理约束层**：确保预测结果符合物理规律
- **多尺度特征融合**：集成不同尺度的空间特征

### 3. 智能训练系统
- **自适应学习率调度**：余弦退火和平台调度
- **梯度裁剪**：防止梯度爆炸
- **早停机制**：避免过拟合
- **数据增强**：提高模型泛化能力

## 📁 项目结构

```
MEEMD-2/
├── improved_meemd.py          # 改进的MEEMD分解模块
├── advanced_dataset.py        # 高级数据集和数据加载器
├── next_gen_model.py          # 下一代ConvLSTM模型
├── advanced_trainer.py        # 高级训练器
├── advanced_evaluator.py      # 综合评估器
├── main_pipeline.py           # 主训练流水线
├── requirements_v2.txt        # 依赖包列表
├── README_v2.md              # 项目说明文档
└── configs/                   # 配置文件目录
    ├── default_config.json   # 默认配置
    └── experiment_configs/    # 实验配置
```

## 🛠️ 安装和环境配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd MEEMD-2
```

### 2. 创建虚拟环境
```bash
# 使用conda
conda create -n meemd-v2 python=3.9
conda activate meemd-v2

# 或使用venv
python -m venv meemd-v2
source meemd-v2/bin/activate  # Linux/Mac
# meemd-v2\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements_v2.txt
```

### 4. 验证安装
```bash
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import PyEMD; print('PyEMD安装成功')"
```

## 🚀 快速开始

### 1. 准备数据
将SST数据文件（NetCDF格式）放在项目根目录，命名为 `SST-V2.nc`

### 2. 运行完整流水线
```bash
python main_pipeline.py
```

### 3. 自定义配置运行
```bash
python main_pipeline.py --config configs/custom_config.json
```

### 4. 恢复训练
```bash
python main_pipeline.py --resume checkpoints_v2/checkpoint_epoch_50.pth
```

## 📊 模型架构详解

### MEEMD分解层
```python
# 自适应参数配置
meemd_config = {
    'ensemble_size': 200,      # 集成数量
    'noise_std': 0.02,         # 噪声标准差
    'max_imf': 8,              # 最大IMF数量
    'quality_threshold': 0.95   # 质量阈值
}
```

### 多尺度ConvLSTM
```python
# 不同IMF使用不同时间窗口
imf_seq_lens = [7, 7, 14, 14, 21, 21, 30, 30]

# 模型配置
model_config = {
    'hidden_dims': [128, 256, 512],
    'kernel_size': 3,
    'use_attention': True,
    'use_physics_constraint': True
}
```

### 损失函数
```python
# 组合损失函数
loss_config = {
    'mse_weight': 0.6,         # MSE权重
    'l1_weight': 0.3,          # L1权重
    'physics_weight': 0.1,     # 物理约束权重
    'imf_weights': [1.0, 1.0, 0.8, 0.8, 0.6, 0.6, 0.4, 0.4]
}
```

## 📈 性能指标

### 预期改进效果
相比原始版本，v2.0版本预期实现：
- **MAE**: 从1.99°C降低到1.2-1.5°C
- **RMSE**: 从2.37°C降低到1.8-2.1°C
- **稳定性**: 样本间误差标准差降低40%+
- **训练效率**: 收敛速度提升30%+

### 评估指标
- **基础指标**: MAE, RMSE, 相关系数, R²分数
- **空间指标**: 空间分布误差、空间相关性
- **IMF指标**: 各分量预测精度、能量比
- **物理一致性**: 温度范围、空间平滑性

## 🔧 高级功能

### 1. 实验配置管理
```python
# 创建自定义配置
config = {
    'meemd': {'ensemble_size': 300},
    'model': {'hidden_dims': [256, 512, 1024]},
    'training': {'num_epochs': 150}
}
```

### 2. 分布式训练支持
```bash
# 多GPU训练
python -m torch.distributed.launch --nproc_per_node=4 main_pipeline.py
```

### 3. 实验跟踪
```python
# 集成wandb
import wandb
wandb.init(project="meemd-sst-v2")
```

### 4. 模型解释性分析
```python
# IMF贡献度分析
from advanced_evaluator import ComprehensiveEvaluator
evaluator = ComprehensiveEvaluator(model, device, lats, lons)
report = evaluator.comprehensive_evaluate(test_loader)
```

## 📊 可视化功能

### 1. 训练过程可视化
- 损失曲线
- 学习率变化
- 梯度范数
- 验证指标

### 2. 预测结果可视化
- 空间误差分布图
- IMF分量分析
- 预测样本对比
- 时间序列对比

### 3. 模型分析可视化
- 注意力权重热图
- 特征重要性分析
- 物理约束效果

## 🧪 实验和调优

### 1. 超参数搜索
```python
# 网格搜索示例
param_grid = {
    'learning_rate': [1e-4, 5e-4, 1e-3],
    'hidden_dims': [[128, 256], [256, 512], [128, 256, 512]],
    'ensemble_size': [100, 200, 300]
}
```

### 2. 消融实验
- MEEMD vs 传统分解方法
- 注意力机制的影响
- 物理约束的作用
- 不同时间窗口长度的效果

### 3. 对比基线
- 传统ConvLSTM
- Transformer-based模型
- 统计预测方法

## 🤝 贡献指南

### 1. 代码规范
- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 添加类型注解
- 编写详细的文档字符串

### 2. 测试
```bash
# 运行测试
pytest tests/ -v --cov=.
```

### 3. 提交流程
1. Fork项目
2. 创建特性分支
3. 提交更改
4. 创建Pull Request

## 📚 参考文献

1. Huang, N. E., et al. (1998). The empirical mode decomposition and the Hilbert spectrum for nonlinear and non-stationary time series analysis.
2. Wu, Z., & Huang, N. E. (2009). Ensemble empirical mode decomposition: a noise-assisted data analysis method.
3. Shi, X., et al. (2015). Convolutional LSTM network: A machine learning approach for precipitation nowcasting.

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目主页: [GitHub Repository URL]

## 🙏 致谢

感谢所有为本项目做出贡献的研究者和开发者，特别是：
- PyEMD库的开发团队
- PyTorch深度学习框架
- 海洋数据提供方

---

**注意**: 这是一个研究项目，仅供学术和研究用途。在生产环境中使用前请进行充分的验证和测试。
