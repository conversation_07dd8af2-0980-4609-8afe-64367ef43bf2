import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import xarray as xr
import os
from tqdm import tqdm

class SSTDataset(Dataset):
    """
    SST数据集类，用于加载MEEMD分解后的IMF组件数据，
    并创建滑动窗口的输入-输出对
    """
    def __init__(self, imf_dir, input_seq_len=14, pred_seq_len=1, 
                 subset='train', cache_data=False, transform=None):
        """
        初始化数据集
        
        参数:
            imf_dir: IMF组件文件所在目录
            input_seq_len: 输入序列长度（天数）
            pred_seq_len: 预测序列长度（天数）
            subset: 'train', 'val', 或 'test'
            cache_data: 是否将数据缓存到内存中
            transform: 数据变换函数
        """
        # 转换为绝对路径
        self.imf_dir = os.path.abspath(imf_dir)
        self.input_seq_len = input_seq_len
        self.pred_seq_len = pred_seq_len
        self.subset = subset
        self.cache_data = cache_data
        self.transform = transform
        
        print(f"使用IMF目录: {self.imf_dir}")
        if not os.path.exists(self.imf_dir):
            raise FileNotFoundError(f"IMF目录不存在: {self.imf_dir}")
        
        # 加载元数据
        metadata_path = os.path.join(os.path.dirname(self.imf_dir), 'meemd_metadata.npy')
        print(f"尝试加载元数据: {metadata_path}")
        if not os.path.exists(metadata_path):
            raise FileNotFoundError(f"元数据文件不存在: {metadata_path}")
            
        self.metadata = np.load(metadata_path, allow_pickle=True).item()
        self.num_imfs = self.metadata['num_imfs']
        print(f"从元数据获取IMF数量: {self.num_imfs}")
        
        # 加载数据集索引信息
        self._load_dataset_info()
        
        # 如果选择缓存数据，则预加载所有IMF组件
        self.imf_data = None
        if self.cache_data:
            self._cache_all_imfs()
    
    def _load_dataset_info(self):
        """加载数据集的基本信息，包括时间索引和可用样本数量"""
        # 加载第一个IMF组件获取基本信息
        first_imf_path = os.path.join(self.imf_dir, 'imf_1.nc')
        print(f"尝试打开IMF文件: {first_imf_path}")
        
        # 检查文件是否存在
        if not os.path.exists(first_imf_path):
            # 尝试列出目录中的文件
            print(f"IMF文件不存在: {first_imf_path}")
            print(f"目录内容: {os.listdir(self.imf_dir)}")
            raise FileNotFoundError(f"IMF文件不存在: {first_imf_path}")
            
        try:
            ds = xr.open_dataset(first_imf_path)
            
            # 获取时间索引
            time_values = ds.time.values
            total_times = len(time_values)
            print(f"时间步数: {total_times}")
            
            # 根据子集类型确定使用的时间范围
            if self.subset == 'train':
                start_idx = 0
                end_idx = int(total_times * 0.8)
            elif self.subset == 'val':
                start_idx = int(total_times * 0.8)
                end_idx = int(total_times * 0.9)
            else:  # test
                start_idx = int(total_times * 0.9)
                end_idx = total_times
            
            self.time_values = time_values[start_idx:end_idx]
            self.start_idx = start_idx
            self.end_idx = end_idx
            
            # 计算可用的样本数量（考虑滑动窗口）
            self.num_samples = max(0, len(self.time_values) - self.input_seq_len - self.pred_seq_len + 1)
            print(f"{self.subset}数据集样本数: {self.num_samples}")
            
            # 获取空间维度信息
            self.lats = ds.latitude.values
            self.lons = ds.longitude.values
            self.spatial_shape = (len(self.lats), len(self.lons))
            print(f"空间形状: {self.spatial_shape}")
            
            ds.close()
        except Exception as e:
            print(f"加载数据集信息出错: {str(e)}")
            raise
        
    def _cache_all_imfs(self):
        """将所有IMF组件加载到内存中"""
        print(f"缓存{self.subset}集的所有IMF组件到内存...")
        
        # 初始化存储所有IMF的数组
        # 形状为 [num_imfs, time_steps, lat, lon]
        self.imf_data = np.zeros((self.num_imfs, 
                                  len(self.time_values), 
                                  self.spatial_shape[0], 
                                  self.spatial_shape[1]), 
                                 dtype=np.float32)
        
        # 加载每个IMF组件
        for i in range(self.num_imfs):
            imf_path = os.path.join(self.imf_dir, f'imf_{i+1}.nc')
            with xr.open_dataset(imf_path) as ds:
                # 提取对应时间范围的数据
                imf_values = ds[f'imf_{i+1}'].values[self.start_idx:self.end_idx]
                self.imf_data[i] = imf_values
    
    def _load_time_slice(self, time_indices):
        """
        加载指定时间索引的所有IMF组件数据
        
        参数:
            time_indices: 时间索引列表
            
        返回:
            shape为[num_imfs, len(time_indices), lat, lon]的数组
        """
        if self.cache_data:
            # 如果已缓存，直接从内存中获取
            return self.imf_data[:, time_indices]
        else:
            # 否则从磁盘加载
            data = np.zeros((self.num_imfs, 
                            len(time_indices), 
                            self.spatial_shape[0], 
                            self.spatial_shape[1]), 
                           dtype=np.float32)
            
            # 加载每个IMF组件的指定时间片
            for i in range(self.num_imfs):
                imf_path = os.path.join(self.imf_dir, f'imf_{i+1}.nc')
                with xr.open_dataset(imf_path) as ds:
                    # 将全局时间索引转换为文件内索引
                    file_indices = [idx + self.start_idx for idx in time_indices]
                    imf_values = ds[f'imf_{i+1}'].isel(time=file_indices).values
                    data[i] = imf_values
            
            return data
    
    def __len__(self):
        """返回数据集中的样本数量"""
        return self.num_samples
    
    def __getitem__(self, idx):
        """
        获取指定索引的样本
        
        参数:
            idx: 样本索引
            
        返回:
            input_tensor: 输入张量，形状为[num_imfs, input_seq_len, lat, lon]
            target_tensor: 目标张量，形状为[num_imfs, pred_seq_len, lat, lon]
        """
        # 计算输入和目标的时间索引
        input_time_indices = list(range(idx, idx + self.input_seq_len))
        target_time_indices = list(range(idx + self.input_seq_len, 
                                        idx + self.input_seq_len + self.pred_seq_len))
        
        # 加载数据
        input_data = self._load_time_slice(input_time_indices)
        target_data = self._load_time_slice(target_time_indices)
        
        # 转换为PyTorch张量
        input_tensor = torch.from_numpy(input_data).float()
        target_tensor = torch.from_numpy(target_data).float()
        
        # 应用变换（如果有）
        if self.transform:
            input_tensor, target_tensor = self.transform(input_tensor, target_tensor)
        
        return input_tensor, target_tensor

def create_sst_dataloaders(imf_dir, batch_size=4, input_seq_len=14, pred_seq_len=1, 
                          num_workers=4, cache_data=False):
    """
    创建训练、验证和测试数据加载器
    
    参数:
        imf_dir: IMF组件文件所在目录
        batch_size: 批次大小
        input_seq_len: 输入序列长度
        pred_seq_len: 预测序列长度
        num_workers: 数据加载的工作进程数
        cache_data: 是否缓存数据到内存
        
    返回:
        train_loader, val_loader, test_loader
    """
    # 创建数据集
    train_dataset = SSTDataset(imf_dir, input_seq_len, pred_seq_len, 
                              subset='train', cache_data=cache_data)
    val_dataset = SSTDataset(imf_dir, input_seq_len, pred_seq_len, 
                            subset='val', cache_data=cache_data)
    test_dataset = SSTDataset(imf_dir, input_seq_len, pred_seq_len, 
                             subset='test', cache_data=cache_data)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, 
                             shuffle=True, num_workers=num_workers,
                             pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, 
                           shuffle=False, num_workers=num_workers,
                           pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, 
                            shuffle=False, num_workers=num_workers,
                            pin_memory=True)
    
    return train_loader, val_loader, test_loader

if __name__ == "__main__":
    # 测试代码
    print("测试数据集加载...")
    imf_dir = 'meemd_spatiotemporal_results/imf_components'
    
    # 显示目录绝对路径
    abs_path = os.path.abspath(imf_dir)
    print(f"IMF目录绝对路径: {abs_path}")
    
    # 检查目录是否存在
    if os.path.exists(abs_path):
        print(f"目录存在，内容: {os.listdir(abs_path)}")
    else:
        print(f"目录不存在")
    
    try:
        # 尝试创建数据集
        dataset = SSTDataset(imf_dir, input_seq_len=14, pred_seq_len=1, subset='train', cache_data=False)
        print(f"数据集创建成功，样本数: {len(dataset)}")
        
        # 尝试加载一个样本
        sample_input, sample_target = dataset[0]
        print(f"样本输入形状: {sample_input.shape}, 样本目标形状: {sample_target.shape}")
    except Exception as e:
        print(f"测试失败: {str(e)}")
