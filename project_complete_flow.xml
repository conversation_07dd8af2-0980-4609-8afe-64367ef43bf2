<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="MEEMD-2项目完整流程图" id="complete-project-flow">
    <mxGraphModel dx="2066" dy="1137" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="0" fold="1" page="1" pageScale="1" pageWidth="2800" pageHeight="4000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="project_title" value="MEEMD-2 海表温度预测项目完整流程" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1ba1e2;strokeColor=#006EAF;fontSize=18;fontStyle=1;fontColor=#ffffff" parent="1" vertex="1">
          <mxGeometry x="50" y="50" width="2700" height="60" as="geometry" />
        </mxCell>
        <mxCell id="stage1_title" value="阶段1: 数据预处理 (meemd.py - preprocess_sst_data)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="150" width="600" height="50" as="geometry" />
        </mxCell>
        <mxCell id="original_data" value="原始SST数据&#xa;SST-V2.nc&#xa;[5114, 100, 140]&#xa;(时间, 纬度, 经度)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="70" y="230" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="temp_convert" value="温度单位转换&#xa;Kelvin → Celsius&#xa;sst = sst - 273.15" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="250" y="230" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="standardize" value="数据标准化&#xa;mean=0, std=1&#xa;[5114, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="430" y="230" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="data_split" value="数据集划分&#xa;训练: [4091, 100, 140]&#xa;验证: [511, 100, 140]&#xa;测试: [512, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="350" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="stage2_title" value="阶段2: MEEMD分解 (meemd.py - apply_spatiotemporal_meemd_to_sst)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="700" y="150" width="700" height="50" as="geometry" />
        </mxCell>
        <mxCell id="spatiotemporal_reshape" value="时空数据重组&#xa;空间点 × 时间点&#xa;[14000, 5114]&#xa;(100×140个空间点)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="720" y="230" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="batch_meemd" value="批处理MEEMD分解&#xa;batch_size=1000&#xa;每批: [1000, 5114]&#xa;→ 6个IMF分量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="920" y="230" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="imf_reconstruct" value="IMF重构为时空格式&#xa;6个IMF组件&#xa;每个: [5114, 100, 140]&#xa;总计: 6×imf_*.nc" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1120" y="230" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="meemd_metadata" value="MEEMD元数据&#xa;num_imfs: 6&#xa;spatial_shape: (100, 140)&#xa;temporal_length: 5114" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="720" y="350" width="326" height="94" as="geometry" />
        </mxCell>
        <mxCell id="stage3_title" value="阶段3: 数据集创建 (dataset.py - SSTDataset)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1450" y="150" width="600" height="50" as="geometry" />
        </mxCell>
        <mxCell id="imf_loading" value="IMF组件加载&#xa;6个文件: imf_1.nc ~ imf_6.nc&#xa;每个: [5114, 100, 140]&#xa;堆叠: [6, 5114, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1458" y="230" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sliding_window" value="滑动窗口创建&#xa;输入: 14天历史数据&#xa;目标: 1天预测数据&#xa;样本数: 4077(train)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1690" y="230" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="sample_shape" value="单个样本形状&#xa;输入: [6, 14, 100, 140]&#xa;目标: [6, 1, 100, 140]&#xa;(IMF, 时间, 纬度, 经度)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1470" y="350" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="batch_shape" value="批处理形状&#xa;输入: [4, 6, 14, 100, 140]&#xa;目标: [4, 6, 1, 100, 140]&#xa;(批次, IMF, 时间, 纬度, 经度)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1690" y="350" width="180" height="90" as="geometry" />
        </mxCell>
        <mxCell id="stage4_title" value="阶段4: 模型训练 (convlstm_model.py - SSTConvLSTMModel)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="50" y="500" width="800" height="50" as="geometry" />
        </mxCell>
        <mxCell id="model_input" value="模型输入&#xa;[4, 6, 14, 100, 140]&#xa;→ 维度调整 →&#xa;[4, 14, 6, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="580" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="convlstm_layers" value="ConvLSTM层&#xa;Layer1: 6→64通道&#xa;Layer2: 64→128通道&#xa;输出: [4, 14, 128, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="270" y="580" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="time_selection" value="最后时间步选择&#xa;[4, 14, 128, 100, 140]&#xa;→ [:, -1] →&#xa;[4, 128, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="470" y="580" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="output_layer" value="输出层处理&#xa;BatchNorm + Dropout&#xa;Conv2d: 128→6通道&#xa;最终输出: [4, 6, 100, 140]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="670" y="580" width="160" height="90" as="geometry" />
        </mxCell>
        <mxCell id="training_process" value="训练过程&#xa;损失函数: MSELoss&#xa;优化器: Adam&#xa;学习率调度: ReduceLROnPlateau&#xa;早停: patience=10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="70" y="700" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="model_params" value="模型参数&#xa;总参数量: 1,047,814&#xa;隐藏维度: [64, 128]&#xa;卷积核大小: 3×3&#xa;Dropout: 0.3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="310" y="700" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="stage5_title" value="阶段5: 模型测试与评估 (test_sst_model.py)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="900" y="500" width="600" height="50" as="geometry" />
        </mxCell>
        <mxCell id="model_loading" value="模型加载&#xa;best_model.pth&#xa;验证损失: 最优&#xa;MAE, MSE, RMSE指标" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="925" y="585" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="test_prediction" value="测试集预测&#xa;输入: [batch, 6, 14, 100, 140]&#xa;输出: [batch, 6, 100, 140]&#xa;测试样本数: 498" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1151" y="585" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="result_evaluation" value="结果评估&#xa;MAE: 1.99°C&#xa;RMSE: 2.37°C&#xa;与原始SST数据对比" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1387" y="585" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="visualization" value="可视化输出&#xa;空间预测图&#xa;时间序列图&#xa;误差分布图" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="920" y="700" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="original_data" target="temp_convert" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="temp_convert" target="standardize" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="standardize" target="data_split" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="data_split" target="spatiotemporal_reshape" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="604" y="395" />
              <mxPoint x="604" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="spatiotemporal_reshape" target="batch_meemd" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="batch_meemd" target="imf_reconstruct" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="imf_reconstruct" target="imf_loading" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1280" y="280" />
              <mxPoint x="1347" y="280" />
              <mxPoint x="1347" y="275" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="imf_loading" target="sliding_window" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="sliding_window" target="batch_shape" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="model_input" target="convlstm_layers" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="convlstm_layers" target="time_selection" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="time_selection" target="output_layer" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="output_layer" target="model_loading" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="model_loading" target="test_prediction" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="test_prediction" target="result_evaluation" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_visualization_title" value="真实数据形状三维可视化" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1ba1e2;strokeColor=#006EAF;fontSize=18;fontStyle=1;fontColor=#ffffff" parent="1" vertex="1">
          <mxGeometry x="50" y="850" width="2700" height="60" as="geometry" />
        </mxCell>
        <mxCell id="original_sst_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#dae8fc;strokeColor=#6c8ebf;size=30" parent="1" vertex="1">
          <mxGeometry x="65" y="940" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="original_sst_label" value="原始SST数据&#xa;[5114, 100, 140]&#xa;时间: 5114天&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;约14年数据" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="100" y="1090" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="spatiotemporal_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ffe6cc;strokeColor=#d79b00;size=35" parent="1" vertex="1">
          <mxGeometry x="350" y="930" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="spatiotemporal_label" value="时空重组数据&#xa;[14000, 5114]&#xa;空间点: 14000个&#xa;(100×140展平)&#xa;时间: 5114天&#xa;用于MEEMD分解" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="350" y="1090" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="imf_4d_real" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#ffcccc;strokeColor=#b85450;size=40" parent="1" vertex="1">
          <mxGeometry x="620" y="910" width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="imf_4d_label" value="IMF分解数据&#xa;[6, 5114, 100, 140]&#xa;IMF分量: 6个&#xa;时间: 5114天&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;6个频率分量" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="620" y="1090" width="220" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sliding_input_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#d5e8d4;strokeColor=#82b366;size=35" parent="1" vertex="1">
          <mxGeometry x="920" y="930" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="sliding_input_label" value="滑动窗口输入&#xa;[6, 14, 100, 140]&#xa;IMF分量: 6个&#xa;历史天数: 14天&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;单个样本输入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="920" y="1090" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="batch_input_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#e1d5e7;strokeColor=#9673a6;size=45" parent="1" vertex="1">
          <mxGeometry x="1230" y="915" width="240" height="170" as="geometry" />
        </mxCell>
        <mxCell id="batch_input_label" value="批处理输入数据&#xa;[4, 6, 14, 100, 140]&#xa;批次大小: 4个样本&#xa;IMF分量: 6个&#xa;历史天数: 14天&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;模型训练输入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1258" y="1100" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="model_output_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#fff2cc;strokeColor=#d6b656;size=35" parent="1" vertex="1">
          <mxGeometry x="1553" y="930" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="model_output_label" value="模型输出数据&#xa;[4, 6, 100, 140]&#xa;批次大小: 4个样本&#xa;IMF分量: 6个&#xa;预测天数: 1天&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;预测结果" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1565" y="1092" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="reconstructed_sst_3d" value="" style="shape=cube;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;darkOpacity=0.05;darkOpacity2=0.1;fillColor=#f8cecc;strokeColor=#b85450;size=30" parent="1" vertex="1">
          <mxGeometry x="1841" y="940" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="reconstructed_sst_label" value="重构SST数据&#xa;[4, 100, 140]&#xa;批次大小: 4个样本&#xa;纬度: 100个点&#xa;经度: 140个点&#xa;最终预测SST&#xa;(IMF分量求和)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="1800" y="1090" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="original_sst_3d" target="spatiotemporal_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="spatiotemporal_3d" target="imf_4d_real" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="imf_4d_real" target="sliding_input_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="sliding_input_3d" target="batch_input_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="batch_input_3d" target="model_output_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3d_arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=4;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="model_output_3d" target="reconstructed_sst_3d" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="transform_1" value="时空重组" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="290" y="990" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform_2" value="MEEMD分解" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="560" y="990" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform_3" value="滑动窗口" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="850" y="990" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform_4" value="批处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="1130" y="990" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform_5" value="ConvLSTM预测" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="1473" y="1019" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transform_6" value="IMF重构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;rotation=15" parent="1" vertex="1">
          <mxGeometry x="1767" y="1013" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="data_stats_title" value="关键数据统计信息" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1ba1e2;strokeColor=#006EAF;fontSize=16;fontStyle=1;fontColor=#ffffff" parent="1" vertex="1">
          <mxGeometry x="50" y="1200" width="2700" height="50" as="geometry" />
        </mxCell>
        <mxCell id="time_stats" value="时间维度统计&#xa;总时间步: 5114天 (约14年)&#xa;训练集: 4091天 (80%)&#xa;验证集: 511天 (10%)&#xa;测试集: 512天 (10%)&#xa;输入序列长度: 14天&#xa;预测长度: 1天&#xa;训练样本数: 4077个" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="100" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="spatial_stats" value="空间维度统计&#xa;纬度点数: 100个&#xa;经度点数: 140个&#xa;总空间点: 14000个&#xa;空间分辨率: 约0.25°&#xa;覆盖区域: 全球海洋&#xa;数据来源: NOAA SST-V2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="350" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="imf_stats" value="IMF分量统计&#xa;IMF分量数: 6个&#xa;IMF_1: 高频分量&#xa;IMF_2-5: 中频分量&#xa;IMF_6: 低频分量+趋势&#xa;分解方法: 2D-MEEMD&#xa;集成数: 100&#xa;噪声标准差: 0.2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#b85450;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="600" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="model_stats" value="模型参数统计&#xa;总参数量: 1,047,814&#xa;ConvLSTM层数: 2层&#xa;隐藏维度: [64, 128]&#xa;卷积核大小: 3×3&#xa;Dropout率: 0.3&#xa;批次大小: 4&#xa;学习率: 0.001" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="850" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="performance_stats" value="性能指标统计&#xa;测试集MAE: 1.99°C&#xa;测试集RMSE: 2.37°C&#xa;训练时间: ~数小时&#xa;推理时间: ~毫秒级&#xa;内存占用: ~15GB(缓存)&#xa;GPU需求: 8GB+&#xa;预测精度: 良好" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1100" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="storage_stats" value="数据存储统计&#xa;原始数据: ~2.8GB&#xa;IMF组件: ~17GB (6×2.8GB)&#xa;预处理数据: ~8.5GB&#xa;模型文件: ~4MB&#xa;结果文件: ~100MB&#xa;总存储需求: ~30GB&#xa;格式: NetCDF4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11" parent="1" vertex="1">
          <mxGeometry x="1350" y="1280" width="200" height="140" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
