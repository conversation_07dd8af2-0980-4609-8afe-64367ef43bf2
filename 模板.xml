<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="第 1 页" id="zK1cGuw8dHphXd9V8AON">
    <mxGraphModel dx="2066" dy="1137" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="0" fold="1" page="1" pageScale="1" pageWidth="2339" pageHeight="3300" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="tylnd04JpYFPQ-XrfWpn-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="tylnd04JpYFPQ-XrfWpn-1" target="tylnd04JpYFPQ-XrfWpn-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tylnd04JpYFPQ-XrfWpn-1" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="504" y="897" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="tylnd04JpYFPQ-XrfWpn-2" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="805" y="1119" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
