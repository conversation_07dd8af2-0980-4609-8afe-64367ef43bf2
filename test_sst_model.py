import os
# 设置环境变量，解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import numpy as np
import matplotlib.pyplot as plt
import xarray as xr
from tqdm import tqdm
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from matplotlib.colors import Normalize
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

# 导入模型和数据集
from convlstm_model import SSTConvLSTMModel, evaluate_model
from dataset import create_sst_dataloaders

def load_best_model(checkpoint_path, num_imfs, device):
    """
    加载训练好的最佳模型
    
    参数:
        checkpoint_path: 模型检查点路径
        num_imfs: IMF组件数量
        device: 计算设备
        
    返回:
        model: 加载的模型
    """
    print(f"从 {checkpoint_path} 加载模型...")
    
    # 检查文件是否存在
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"模型检查点不存在: {checkpoint_path}")
    
    # 创建模型实例
    model = SSTConvLSTMModel(
        num_imfs=num_imfs,
        hidden_dims=[64, 128],
        kernel_size=3,
        dropout=0.3
    ).to(device)
    
    # 加载模型权重
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    print(f"成功加载模型，验证损失: {checkpoint['val_loss']:.6f}")
    print(f"验证指标 - MAE: {checkpoint['val_mae']:.6f}, MSE: {checkpoint['val_mse']:.6f}, RMSE: {checkpoint['val_rmse']:.6f}")
    
    return model

def predict_and_evaluate(model, test_loader, device):
    """
    在测试集上预测并评估模型性能
    
    参数:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 计算设备
        
    返回:
        predictions: 预测结果列表
        targets: 真实值列表
        metrics: 评估指标
    """
    model.eval()
    predictions = []
    targets = []
    
    print("在测试集上进行预测...")
    
    with torch.no_grad():
        for inputs, target in tqdm(test_loader):
            # 将数据移到设备上
            inputs = inputs.to(device)
            
            # 前向传播
            outputs = model(inputs)
            
            # 收集预测和真实值
            predictions.append(outputs.cpu().numpy())
            targets.append(target[:, :, 0].cpu().numpy())  # 只取第一个时间步
    
    # 将预测和真实值转换为数组
    predictions = np.concatenate(predictions, axis=0)
    targets = np.concatenate(targets, axis=0)
    
    # 计算评估指标
    mae = np.mean(np.abs(predictions - targets))
    mse = np.mean((predictions - targets) ** 2)
    rmse = np.sqrt(mse)
    
    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse
    }
    
    print(f"测试集评估 - MAE: {mae:.6f}, MSE: {mse:.6f}, RMSE: {rmse:.6f}")
    
    return predictions, targets, metrics

def load_residue(imf_dir):
    """
    加载MEEMD分解的残差项
    
    参数:
        imf_dir: IMF组件文件所在目录
        
    返回:
        residue: 残差项数组
    """
    residue_path = os.path.join(os.path.dirname(imf_dir), 'residue.nc')
    print(f"尝试加载残差项: {residue_path}")
    
    if not os.path.exists(residue_path):
        print(f"警告: 残差项文件不存在: {residue_path}")
        print("将尝试从原始SST数据中获取...")
        
        # 尝试加载原始SST数据
        original_sst_path = os.path.join(os.path.dirname(imf_dir), 'original_sst.nc')
        if os.path.exists(original_sst_path):
            try:
                with xr.open_dataset(original_sst_path) as ds:
                    print("成功加载原始SST数据")
                    # 获取SST均值作为残差的近似值
                    residue = ds['sst'].mean(dim='time').values
                    return residue
            except Exception as e:
                print(f"加载原始SST数据失败: {str(e)}")
                
        # 如果无法获取残差或原始数据，返回一个估计值
        print("无法获取残差项，将使用估计值26°C作为基准温度")
        return 26.0  # 估计的海表温度基准值
    
    try:
        with xr.open_dataset(residue_path) as ds:
            residue = ds['residue'].values
            print(f"成功加载残差项，形状: {residue.shape}")
            return residue
    except Exception as e:
        print(f"加载残差项失败: {str(e)}")
        print("将使用估计值26°C作为基准温度")
        return 26.0  # 估计的海表温度基准值

def load_original_sst(test_dataset):
    """
    加载原始SST数据
    
    参数:
        test_dataset: 测试数据集实例，用于获取时间索引
        
    返回:
        original_sst: 原始SST数据数组，如果加载失败则返回None
    """
    # 尝试确定原始SST数据路径
    imf_dir = os.path.dirname(test_dataset.imf_dir)
    original_sst_path = os.path.join(imf_dir, 'SST-V2.nc')
    
    print(f"尝试加载原始SST数据: {original_sst_path}")
    
    if not os.path.exists(original_sst_path):
        print(f"原始SST数据文件不存在: {original_sst_path}")
        return None
    
    try:
        with xr.open_dataset(original_sst_path) as ds:
            # 获取测试集使用的时间索引
            test_time_indices = test_dataset.time_values
            
            # 从原始数据中提取对应的时间步
            # 注意：这里假设原始数据和测试集使用相同的时间索引
            # 如果不是，可能需要进行时间匹配
            original_sst = ds['analysed_sst'].sel(time=test_time_indices).values
            
            # 将开尔文温度转换为摄氏度
            original_sst = original_sst - 273.15
            
            print(f"成功加载原始SST数据，形状: {original_sst.shape}")
            print(f"温度范围（摄氏度）: {np.min(original_sst):.2f}°C 到 {np.max(original_sst):.2f}°C")
            
            # 确保形状与测试集匹配
            if len(original_sst) != len(test_dataset):
                print(f"警告: 原始SST数据长度 ({len(original_sst)}) 与测试集长度 ({len(test_dataset)}) 不匹配")
                print(f"将截取前 {len(test_dataset)} 个样本")
                original_sst = original_sst[:len(test_dataset)]
            
            return original_sst
    except Exception as e:
        print(f"加载原始SST数据失败: {str(e)}")
        return None

def visualize_spatial_predictions(predictions, targets, lats, lons, residue=None, original_sst=None, sample_indices=None, save_dir=None):
    """
    可视化空间预测结果
    
    参数:
        predictions: 预测结果数组 [samples, num_imfs, lat, lon]
        targets: 真实值数组 [samples, num_imfs, lat, lon]
        lats: 纬度数组
        lons: 经度数组
        residue: 残差项，用于重构完整的SST
        original_sst: 原始SST数据，如果提供则直接使用而不是重构
        sample_indices: 要可视化的样本索引列表，如果为None则随机选择5个
        save_dir: 保存目录，如果为None则显示图像
    """
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 如果没有指定样本索引，则随机选择5个
    if sample_indices is None:
        num_samples = min(5, predictions.shape[0])
        sample_indices = np.random.choice(predictions.shape[0], num_samples, replace=False)
    
    # 创建地图投影
    projection = ccrs.PlateCarree()
    
    for i, idx in enumerate(sample_indices):
        # 计算所有IMF的总和
        pred_sum = np.sum(predictions[idx], axis=0)
        
        # 如果有原始SST数据，直接使用
        if original_sst is not None and idx < len(original_sst):
            true_sum = original_sst[idx]
            print(f"使用原始SST数据进行可视化，样本索引: {idx}")
        else:
            # 否则使用IMF重构
            true_sum = np.sum(targets[idx], axis=0)
            print(f"使用IMF重构SST数据进行可视化，样本索引: {idx}")
            
            # 如果有残差项，加入残差以重构完整的SST
            if residue is not None:
                if isinstance(residue, (int, float)):
                    # 如果残差是标量，直接加到所有位置
                    true_sum += residue
                else:
                    # 如果残差是数组，确保形状匹配
                    if residue.shape == true_sum.shape:
                        true_sum += residue
                    else:
                        print(f"警告: 残差形状 {residue.shape} 与真实值形状 {true_sum.shape} 不匹配")
        
        # 如果有残差项，加入残差以重构完整的预测SST
        if residue is not None:
            if isinstance(residue, (int, float)):
                # 如果残差是标量，直接加到所有位置
                pred_sum += residue
            else:
                # 如果残差是数组，确保形状匹配
                if residue.shape == pred_sum.shape:
                    pred_sum += residue
                else:
                    print(f"警告: 残差形状 {residue.shape} 与预测形状 {pred_sum.shape} 不匹配")
        
        # 计算差异
        diff = true_sum - pred_sum
        
        # 计算颜色范围 (适合SST的温度范围)
        vmin = min(np.min(pred_sum), np.min(true_sum))
        vmax = max(np.max(pred_sum), np.max(true_sum))
        # 确保合理的SST温度范围
        if vmin < 0 or vmax > 40:
            print(f"警告: 异常的SST温度范围: {vmin:.2f}°C 到 {vmax:.2f}°C")
            # 如果范围不合理，使用更合理的范围
            if vmin < 0:
                vmin = 0
            if vmax > 40:
                vmax = 40
        
        norm = Normalize(vmin=vmin, vmax=vmax)
        
        # 创建图像
        fig = plt.figure(figsize=(18, 6))
        
        # 预测的SST
        ax1 = fig.add_subplot(1, 3, 1, projection=projection)
        ax1.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax1.add_feature(cfeature.BORDERS, linewidth=0.5)
        ax1.gridlines(draw_labels=True, linewidth=0.5, alpha=0.5)
        im1 = ax1.pcolormesh(lons, lats, pred_sum, cmap='viridis', norm=norm, transform=projection)
        ax1.set_title('预测的SST')
        plt.colorbar(im1, ax=ax1, orientation='horizontal', pad=0.05, label='温度 (°C)')
        
        # 真实的SST
        ax2 = fig.add_subplot(1, 3, 2, projection=projection)
        ax2.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax2.add_feature(cfeature.BORDERS, linewidth=0.5)
        ax2.gridlines(draw_labels=True, linewidth=0.5, alpha=0.5)
        im2 = ax2.pcolormesh(lons, lats, true_sum, cmap='viridis', norm=norm, transform=projection)
        title = '真实的SST (原始数据)' if original_sst is not None else '真实的SST (IMF重构)'
        ax2.set_title(title)
        plt.colorbar(im2, ax=ax2, orientation='horizontal', pad=0.05, label='温度 (°C)')
        
        # 差异
        ax3 = fig.add_subplot(1, 3, 3, projection=projection)
        ax3.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax3.add_feature(cfeature.BORDERS, linewidth=0.5)
        ax3.gridlines(draw_labels=True, linewidth=0.5, alpha=0.5)
        # 使用更合适的差异范围
        diff_max = max(abs(np.min(diff)), abs(np.max(diff)))
        diff_norm = Normalize(vmin=-diff_max, vmax=diff_max)
        im3 = ax3.pcolormesh(lons, lats, diff, cmap='RdBu_r', norm=diff_norm, transform=projection)
        ax3.set_title('差异 (真实 - 预测)')
        plt.colorbar(im3, ax=ax3, orientation='horizontal', pad=0.05, label='温度差 (°C)')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'spatial_prediction_{i+1}.png'), dpi=300)
            plt.close()
        else:
            plt.show()

def visualize_time_series(predictions, targets, lats, lons, residue=None, original_sst=None, locations=None, save_dir=None):
    """
    可视化特定位置的时间序列预测
    
    参数:
        predictions: 预测结果数组 [samples, num_imfs, lat, lon]
        targets: 真实值数组 [samples, num_imfs, lat, lon]
        lats: 纬度数组
        lons: 经度数组
        residue: 残差项，用于重构完整的SST
        original_sst: 原始SST数据，如果提供则直接使用而不是重构
        locations: 要可视化的位置列表，格式为[(lat_idx, lon_idx, name)]，如果为None则选择预定义的位置
        save_dir: 保存目录，如果为None则显示图像
    """
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 如果没有指定位置，则选择一些代表性位置
    if locations is None:
        # 选择几个代表性位置的索引
        lat_center = len(lats) // 2
        lon_center = len(lons) // 2
        locations = [
            (lat_center, lon_center, "中心点"),
            (lat_center - len(lats)//4, lon_center, "北部"),
            (lat_center + len(lats)//4, lon_center, "南部"),
            (lat_center, lon_center - len(lons)//4, "西部"),
            (lat_center, lon_center + len(lons)//4, "东部")
        ]
    
    # 对每个位置绘制时间序列
    for lat_idx, lon_idx, name in locations:
        # 提取该位置所有时间步的预测值
        pred_series = np.sum(predictions[:, :, lat_idx, lon_idx], axis=1)  # 所有IMF的和
        
        # 如果有原始SST数据，直接使用
        if original_sst is not None:
            # 确保原始数据长度与预测长度匹配
            if len(original_sst) >= len(pred_series):
                true_series = original_sst[:len(pred_series), lat_idx, lon_idx]
                print(f"使用原始SST数据进行时间序列可视化，位置: {name}")
            else:
                print(f"警告: 原始SST数据长度不足，使用IMF重构数据，位置: {name}")
                true_series = np.sum(targets[:, :, lat_idx, lon_idx], axis=1)
                # 如果有残差项，加入残差
                if residue is not None:
                    if isinstance(residue, (int, float)):
                        true_series += residue
                    else:
                        true_series += residue[lat_idx, lon_idx]
        else:
            # 否则使用IMF重构
            true_series = np.sum(targets[:, :, lat_idx, lon_idx], axis=1)
            print(f"使用IMF重构SST数据进行时间序列可视化，位置: {name}")
            
            # 如果有残差项，加入残差以重构完整的SST
            if residue is not None:
                if isinstance(residue, (int, float)):
                    # 如果残差是标量，直接加到所有位置
                    true_series += residue
                else:
                    # 如果残差是数组，获取该位置的残差值
                    true_series += residue[lat_idx, lon_idx]
        
        # 如果有残差项，加入残差以重构完整的预测SST
        if residue is not None:
            if isinstance(residue, (int, float)):
                # 如果残差是标量，直接加到所有位置
                pred_series += residue
            else:
                # 如果残差是数组，获取该位置的残差值
                pred_series += residue[lat_idx, lon_idx]
        
        # 创建时间序列图
        plt.figure(figsize=(12, 6))
        plt.plot(true_series, 'b-', label='真实值' + (' (原始数据)' if original_sst is not None else ' (IMF重构)'))
        plt.plot(pred_series, 'r--', label='预测值')
        plt.xlabel('时间步')
        plt.ylabel('SST (°C)')
        plt.title(f'位置: {name} ({lats[lat_idx]:.2f}°N, {lons[lon_idx]:.2f}°E)')
        plt.legend()
        plt.grid(True)
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'time_series_{name}.png'), dpi=300)
            plt.close()
        else:
            plt.show()

def visualize_error_distribution(predictions, targets, residue=None, original_sst=None, save_path=None):
    """
    可视化预测误差分布
    
    参数:
        predictions: 预测结果数组 [samples, num_imfs, lat, lon]
        targets: 真实值数组 [samples, num_imfs, lat, lon]
        residue: 残差项，用于重构完整的SST (不影响误差计算)
        original_sst: 原始SST数据，如果提供则直接使用而不是重构
        save_path: 保存路径，如果为None则显示图像
    """
    # 计算所有IMF的总和
    pred_sum = np.sum(predictions, axis=1)  # [samples, lat, lon]
    
    # 如果有原始SST数据，直接使用
    if original_sst is not None:
        # 确保原始数据长度与预测长度匹配
        if len(original_sst) > len(pred_sum):
            true_sum = original_sst[:len(pred_sum)]
            print(f"使用原始SST数据计算误差分布 (截取到 {len(pred_sum)} 个样本)")
        elif len(original_sst) < len(pred_sum):
            # 如果原始数据长度小于预测长度，截取预测数据
            pred_sum = pred_sum[:len(original_sst)]
            true_sum = original_sst
            print(f"预测数据截取到 {len(original_sst)} 个样本以匹配原始SST数据长度")
        else:
            true_sum = original_sst
            print("使用原始SST数据计算误差分布")
    else:
        # 否则使用IMF重构
        true_sum = np.sum(targets, axis=1)
        print("使用IMF重构SST数据计算误差分布")
    
    # 如果有残差项，加入残差以重构完整的SST
    if residue is not None and original_sst is None:  # 只有在使用IMF重构时才需要加残差
        if isinstance(residue, (int, float)):
            # 如果残差是标量，直接加到所有位置
            pred_sum += residue
            true_sum += residue
        else:
            # 如果残差是数组，确保形状匹配
            if len(pred_sum.shape) == 3 and len(residue.shape) == 2:
                # 广播残差到所有时间步
                pred_sum += residue[np.newaxis, :, :]
                true_sum += residue[np.newaxis, :, :]
            else:
                print(f"警告: 残差形状 {residue.shape} 与数据形状不匹配")
    
    # 计算误差
    errors = (true_sum - pred_sum).flatten()
    
    # 创建误差分布直方图
    plt.figure(figsize=(10, 6))
    plt.hist(errors, bins=50, alpha=0.7, color='blue')
    plt.xlabel('预测误差 (°C)')
    plt.ylabel('频率')
    title = 'SST预测误差分布' + (' (基于原始数据)' if original_sst is not None else ' (基于IMF重构)')
    plt.title(title)
    plt.axvline(x=0, color='r', linestyle='--')
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_error = np.mean(errors)
    std_error = np.std(errors)
    plt.text(0.02, 0.95, f'均值: {mean_error:.4f}°C\n标准差: {std_error:.4f}°C',
             transform=plt.gca().transAxes, bbox=dict(facecolor='white', alpha=0.8))
    
    if save_path:
        plt.savefig(save_path, dpi=300)
        plt.close()
    else:
        plt.show()

def evaluate_with_original_data(predictions, original_sst, residue=None, num_samples=10):
    """
    随机选取样本，计算预测结果与原始SST数据的评估指标
    
    参数:
        predictions: 预测结果数组 [samples, num_imfs, lat, lon]
        original_sst: 原始SST数据
        residue: 残差项，用于重构完整的SST
        num_samples: 随机选取的样本数量
        
    返回:
        metrics: 评估指标字典
    """
    if original_sst is None:
        print("警告: 无法获取原始SST数据，无法计算评估指标")
        return None
    
    # 确保样本数量不超过可用数据量
    num_samples = min(num_samples, len(predictions), len(original_sst))
    
    # 随机选取样本索引
    np.random.seed(42)  # 固定随机种子以便结果可复现
    sample_indices = np.random.choice(len(predictions), num_samples, replace=False)
    
    print(f"随机选取 {num_samples} 个样本计算评估指标:")
    print(f"样本索引: {sample_indices}")
    
    # 计算所有IMF的总和
    pred_samples = np.sum(predictions[sample_indices], axis=1)  # [num_samples, lat, lon]
    
    # 获取对应的原始SST数据
    true_samples = original_sst[sample_indices]
    
    # 如果有残差项，加入残差以重构完整的预测SST
    if residue is not None:
        if isinstance(residue, (int, float)):
            # 如果残差是标量，直接加到所有位置
            pred_samples += residue
        else:
            # 如果残差是数组，确保形状匹配
            if len(pred_samples.shape) == 3 and len(residue.shape) == 2:
                # 广播残差到所有样本
                pred_samples += residue[np.newaxis, :, :]
            else:
                print(f"警告: 残差形状 {residue.shape} 与数据形状不匹配")
    
    # 计算评估指标
    mae = np.mean(np.abs(pred_samples - true_samples))
    mse = np.mean((pred_samples - true_samples) ** 2)
    rmse = np.sqrt(mse)
    
    # 计算每个样本的评估指标
    sample_metrics = []
    for i in range(num_samples):
        sample_mae = np.mean(np.abs(pred_samples[i] - true_samples[i]))
        sample_mse = np.mean((pred_samples[i] - true_samples[i]) ** 2)
        sample_rmse = np.sqrt(sample_mse)
        sample_metrics.append({
            'index': sample_indices[i],
            'mae': sample_mae,
            'mse': sample_mse,
            'rmse': sample_rmse
        })
    
    # 输出每个样本的评估指标
    print("\n样本评估指标:")
    for metric in sample_metrics:
        print(f"样本 {metric['index']}: MAE={metric['mae']:.6f}, MSE={metric['mse']:.6f}, RMSE={metric['rmse']:.6f}")
    
    # 输出总体评估指标
    print(f"\n总体评估指标 (基于原始SST数据):")
    print(f"MAE: {mae:.6f}, MSE: {mse:.6f}, RMSE: {rmse:.6f}")
    
    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'sample_metrics': sample_metrics
    }
    
    return metrics

def main():
    # 设置随机种子以保证可重复性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 数据集参数
    imf_dir = 'meemd_spatiotemporal_results/imf_components'
    batch_size = 8  # 可以适当增大，因为只是测试
    input_seq_len = 14
    pred_seq_len = 1
    
    # 加载元数据以获取IMF数量
    metadata_path = os.path.join(os.path.dirname(imf_dir), 'meemd_metadata.npy')
    metadata = np.load(metadata_path, allow_pickle=True).item()
    num_imfs = metadata['num_imfs']
    print(f"IMF组件数量: {num_imfs}")
    
    # 创建测试数据加载器
    print("创建测试数据加载器...")
    _, _, test_loader = create_sst_dataloaders(
        imf_dir=imf_dir,
        batch_size=batch_size,
        input_seq_len=input_seq_len,
        pred_seq_len=pred_seq_len,
        num_workers=4,
        cache_data=False
    )
    
    # 获取经纬度信息
    # 从测试数据集中获取
    test_dataset = test_loader.dataset
    lats = test_dataset.lats
    lons = test_dataset.lons
    
    # 加载残差项，用于重构完整的SST
    residue = load_residue(imf_dir)
    
    # 尝试加载原始SST数据
    original_sst = load_original_sst(test_dataset)
    
    # 加载最佳模型
    checkpoint_path = 'sst_convlstm_checkpoints/best_model.pth'
    model = load_best_model(checkpoint_path, num_imfs, device)
    
    # 在测试集上预测并评估
    predictions, targets, metrics = predict_and_evaluate(model, test_loader, device)
    
    # 创建可视化结果目录
    results_dir = 'test_results'
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    # 随机选取样本计算与原始数据的评估指标
    print("\n计算与原始SST数据的评估指标...")
    original_metrics = evaluate_with_original_data(
        predictions=predictions,
        original_sst=original_sst,
        residue=residue,
        num_samples=10
    )
    
    # 将评估指标保存到文件
    if original_metrics:
        metrics_path = os.path.join(results_dir, 'original_metrics.txt')
        with open(metrics_path, 'w') as f:
            f.write(f"总体评估指标 (基于原始SST数据):\n")
            f.write(f"MAE: {original_metrics['mae']:.6f}\n")
            f.write(f"MSE: {original_metrics['mse']:.6f}\n")
            f.write(f"RMSE: {original_metrics['rmse']:.6f}\n\n")
            
            f.write("样本评估指标:\n")
            for metric in original_metrics['sample_metrics']:
                f.write(f"样本 {metric['index']}: MAE={metric['mae']:.6f}, MSE={metric['mse']:.6f}, RMSE={metric['rmse']:.6f}\n")
        
        print(f"评估指标已保存到 {metrics_path}")
    
    # 可视化空间预测结果
    print("\n可视化空间预测结果...")
    visualize_spatial_predictions(
        predictions=predictions,
        targets=targets,
        lats=lats,
        lons=lons,
        residue=residue,
        original_sst=original_sst,
        save_dir=os.path.join(results_dir, 'spatial')
    )
    
    # 可视化时间序列预测
    print("\n可视化时间序列预测...")
    visualize_time_series(
        predictions=predictions,
        targets=targets,
        lats=lats,
        lons=lons,
        residue=residue,
        original_sst=original_sst,
        save_dir=os.path.join(results_dir, 'time_series')
    )
    
    # 可视化误差分布
    print("\n可视化误差分布...")
    visualize_error_distribution(
        predictions=predictions,
        targets=targets,
        residue=residue,
        original_sst=original_sst,
        save_path=os.path.join(results_dir, 'error_distribution.png')
    )
    
    print("\n测试和可视化完成!")

if __name__ == "__main__":
    main() 