import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import pandas as pd

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

def explore_netcdf(file_path):
    """
    探索NetCDF文件的结构和内容
    """
    print(f"检查文件: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path) / (1024 * 1024):.2f} MB")
    
    # 打开NetCDF文件
    try:
        ds = xr.open_dataset(file_path)
        
        # 基本信息
        print("\n数据集基本信息:")
        print(ds.info())
        
        # 查看数据集的维度
        print("\n数据集维度:")
        for dim_name, dim_size in ds.dims.items():
            print(f"  {dim_name}: {dim_size}")
        
        # 查看数据集中的变量
        print("\n数据集变量:")
        for var_name, var in ds.variables.items():
            print(f"  {var_name}:")
            print(f"    维度: {var.dims}")
            print(f"    形状: {var.shape}")
            print(f"    数据类型: {var.dtype}")
            if hasattr(var, 'units'):
                print(f"    单位: {var.units}")
            print(f"    属性: {var.attrs}")
        
        # 检查数据中的缺失值
        print("\n缺失值检查:")
        for var_name, var in ds.data_vars.items():
            if np.issubdtype(var.dtype, np.number):
                nan_count = np.isnan(var.values).sum()
                total_count = var.size
                print(f"  {var_name}: {nan_count} 缺失值 ({nan_count/total_count*100:.2f}%)")
        
        # 显示时间范围（如果有时间维度）
        if 'time' in ds.dims:
            print("\n时间范围:")
            print(f"  开始: {ds.time.values[0]}")
            print(f"  结束: {ds.time.values[-1]}")
            print(f"  时间步长: {len(ds.time)}")
            if len(ds.time) > 1:
                time_diff = pd.to_datetime(ds.time.values[1]) - pd.to_datetime(ds.time.values[0])
                print(f"  时间间隔: {time_diff}")
        
        # 经纬度范围（如果有）
        if 'lat' in ds.dims or 'latitude' in ds.dims:
            lat_name = 'lat' if 'lat' in ds.dims else 'latitude'
            print(f"\n纬度范围: {ds[lat_name].values.min()} 到 {ds[lat_name].values.max()}")
        
        if 'lon' in ds.dims or 'longitude' in ds.dims:
            lon_name = 'lon' if 'lon' in ds.dims else 'longitude'
            print(f"经度范围: {ds[lon_name].values.min()} 到 {ds[lon_name].values.max()}")
        
        # 可视化数据（可选）
        # 如果是SST数据，绘制第一个时间步的全球海表温度
        if 'sst' in ds.data_vars or 'SST' in ds.data_vars:
            var_name = 'sst' if 'sst' in ds.data_vars else 'SST'
            plt.figure(figsize=(12, 6))
            if 'time' in ds[var_name].dims:
                ds[var_name].isel(time=0).plot()
                plt.title(f"海表温度分布 (第一个时间步)")
            else:
                ds[var_name].plot()
                plt.title(f"海表温度分布")
            plt.savefig('sst_visualization.png')
            print("\n已保存海表温度可视化图到 'sst_visualization.png'")
        
        return ds
    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

if __name__ == "__main__":
    file_path = "SST-V2.nc"
    ds = explore_netcdf(file_path)
    
    # 关闭数据集
    if ds is not None:
        ds.close() 