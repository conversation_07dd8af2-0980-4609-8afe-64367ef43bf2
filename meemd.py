import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from PyEMD import EEMD
import os
from tqdm import tqdm
from sklearn.preprocessing import StandardScaler
import multiprocessing as mp
import pandas as pd

# 数据预处理函数
def preprocess_sst_data(file_path, output_dir='preprocessed_data'):
    """
    预处理SST数据，包括读取、标准化和划分
    
    参数:
        file_path: NetCDF文件路径
        output_dir: 输出目录
    
    返回:
        预处理后的数据集
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取数据
    print("读取SST数据...")
    ds = xr.open_dataset(file_path)
    
    # 确认变量名
    sst_var_name = 'analysed_sst' if 'analysed_sst' in ds.data_vars else 'sst'
    
    # 提取SST数据和坐标
    sst = ds[sst_var_name].values
    time = ds['time'].values
    lat = ds['latitude'].values
    lon = ds['longitude'].values
    
    # 将开尔文转换为摄氏度
    if ds[sst_var_name].attrs.get('units') == 'kelvin':
        print("将温度从开尔文转换为摄氏度...")
        sst = sst - 273.15
    
    # 保存原始数据统计信息，用于后续反标准化
    sst_mean = np.nanmean(sst)
    sst_std = np.nanstd(sst)
    
    # 标准化数据
    print("标准化SST数据...")
    sst_standardized = (sst - sst_mean) / sst_std
    
    # 创建标准化后的xarray Dataset
    ds_standardized = xr.Dataset(
        data_vars={
            'sst_standardized': (['time', 'latitude', 'longitude'], sst_standardized),
            'sst_original': (['time', 'latitude', 'longitude'], sst)
        },
        coords={
            'time': time,
            'latitude': lat,
            'longitude': lon
        }
    )
    
    # 保存预处理参数，用于后续反标准化
    np.savez(
        os.path.join(output_dir, 'preprocessing_params.npz'),
        sst_mean=sst_mean,
        sst_std=sst_std
    )
    
    # 数据集划分（训练、验证、测试）
    total_time_steps = len(time)
    train_end = int(total_time_steps * 0.8)
    val_end = int(total_time_steps * 0.9)
    
    train_ds = ds_standardized.isel(time=slice(0, train_end))
    val_ds = ds_standardized.isel(time=slice(train_end, val_end))
    test_ds = ds_standardized.isel(time=slice(val_end, None))
    
    # 保存划分后的数据集
    print(f"保存预处理后的数据集到 {output_dir}...")
    train_ds.to_netcdf(os.path.join(output_dir, 'train_data.nc'))
    val_ds.to_netcdf(os.path.join(output_dir, 'val_data.nc'))
    test_ds.to_netcdf(os.path.join(output_dir, 'test_data.nc'))
    ds_standardized.to_netcdf(os.path.join(output_dir, 'full_standardized_data.nc'))
    
    print(f"预处理完成。训练集: {train_ds.time.size}个时间步, "
          f"验证集: {val_ds.time.size}个时间步, 测试集: {test_ds.time.size}个时间步")
    
    return ds_standardized

# 一维EEMD分解函数
def perform_1d_eemd(signal, ensemble_size=100, noise_std=0.1):
    """
    对一维信号进行EEMD分解
    
    参数:
        signal: 一维信号数组
        ensemble_size: 集合成员数量
        noise_std: 添加噪声的标准差比例
        
    返回:
        imfs: 分解得到的IMF数组
    """
    # 检查信号是否包含NaN值
    if np.any(np.isnan(signal)):
        return np.zeros((1, len(signal)))  # 如果有NaN，返回全零数组
    
    # 初始化EEMD
    eemd = EEMD()
    eemd.noise_seed(12345)  # 设置随机种子以保证可重复性
    
    # 设置EEMD参数
    eemd.ensemble_size = ensemble_size
    eemd.noise_width = noise_std * np.std(signal)
    
    # 进行EEMD分解
    try:
        imfs = eemd(signal)
        return imfs
    except Exception as e:
        print(f"EEMD分解出错: {e}")
        return np.zeros((1, len(signal)))  # 出错时返回全零数组

# 二维MEEMD分解类
class MEEMD2D:
    def __init__(self, ensemble_size=100, noise_std=0.1, n_jobs=1):
        """
        初始化二维MEEMD分解器
        
        参数:
            ensemble_size: EEMD集合成员数量
            noise_std: 添加噪声的标准差比例
            n_jobs: 并行处理的作业数量
        """
        self.ensemble_size = ensemble_size
        self.noise_std = noise_std
        self.n_jobs = n_jobs
        
    def _process_column(self, args):
        """并行处理单列的辅助函数"""
        col_idx, col_data = args
        return col_idx, perform_1d_eemd(col_data, self.ensemble_size, self.noise_std)
    
    def _process_row(self, args):
        """并行处理单行的辅助函数"""
        row_idx, j_idx, row_data = args
        return row_idx, j_idx, perform_1d_eemd(row_data, self.ensemble_size, self.noise_std)
    
    def decompose(self, data_2d):
        """
        对二维数据进行MEEMD分解
        
        参数:
            data_2d: 二维数据数组，形状为(m, n)
            
        返回:
            final_imfs: 最终的二维IMF分量列表
        """
        m, n = data_2d.shape
        print(f"处理{m}×{n}的二维数据...")
        
        # 第一次分解（沿列方向）
        print("第一次分解（沿列方向）...")
        column_tasks = [(i, data_2d[:, i]) for i in range(n)]
        
        if self.n_jobs > 1:
            with mp.Pool(self.n_jobs) as pool:
                results = list(tqdm(pool.imap(self._process_column, column_tasks), total=len(column_tasks)))
        else:
            results = [self._process_column(task) for task in tqdm(column_tasks)]
        
        # 整理第一次分解的结果
        column_imfs = {}
        max_imf_count = 0
        
        for col_idx, imfs in results:
            column_imfs[col_idx] = imfs
            max_imf_count = max(max_imf_count, len(imfs))
        
        # 创建伪IMF矩阵 g_j(m,n)
        g_matrices = []
        for j in range(max_imf_count):
            g_j = np.zeros((m, n))
            for col_idx in range(n):
                imfs = column_imfs[col_idx]
                if j < len(imfs):
                    g_j[:, col_idx] = imfs[j]
            g_matrices.append(g_j)
        
        # 第二次分解（沿行方向）
        print("第二次分解（沿行方向）...")
        row_tasks = []
        for j, g_j in enumerate(g_matrices):
            for i in range(m):
                row_tasks.append((i, j, g_j[i, :]))
        
        if self.n_jobs > 1:
            with mp.Pool(self.n_jobs) as pool:
                h_results = list(tqdm(pool.imap(self._process_row, row_tasks), total=len(row_tasks)))
        else:
            h_results = [self._process_row(task) for task in tqdm(row_tasks)]
        
        # 整理第二次分解的结果，创建h_j,k(m,n)
        h_matrices = {}
        max_k_count = 0
        
        for row_idx, j_idx, imfs in h_results:
            for k, imf in enumerate(imfs):
                key = (j_idx, k)
                if key not in h_matrices:
                    h_matrices[key] = np.zeros((m, n))
                h_matrices[key][row_idx, :] = imf
                max_k_count = max(max_k_count, k+1)
        
        # 根据可比较最小尺度原则组合分量
        print("根据可比较最小尺度原则组合IMF分量...")
        J = max_imf_count
        K = max_k_count
        final_imfs = []
        
        for i in range(min(J, K)):
            C_i = np.zeros((m, n))
            
            # 按照公式: C_i = sum_{k=i}^{K} h_{i,k} + sum_{j=i+1}^{J} h_{j,i}
            for k in range(i, K):
                key = (i, k)
                if key in h_matrices:
                    C_i += h_matrices[key]
                    
            for j in range(i+1, J):
                key = (j, i)
                if key in h_matrices:
                    C_i += h_matrices[key]
            
            final_imfs.append(C_i)
            
        return final_imfs

# 将MEEMD应用于SST数据(时空混合方式)
def apply_spatiotemporal_meemd_to_sst(sst_data, output_dir='meemd_spatiotemporal_results', 
                                    ensemble_size=100, noise_std=0.1, n_jobs=1,
                                    batch_size=1000, use_memmap=True, enable_gc=True):
    """
    对SST数据应用时空混合MEEMD分解（内存优化版本）
    
    参数:
        sst_data: xarray Dataset，包含标准化后的SST数据
        output_dir: 输出目录
        ensemble_size: EEMD集合成员数量
        noise_std: 噪声标准差比例
        n_jobs: 并行处理的作业数量
        batch_size: 每批处理的空间点数量，用于降低内存占用
        use_memmap: 是否使用内存映射临时文件降低内存占用
        enable_gc: 是否启用强制垃圾回收
        
    返回:
        meemd_ds: 包含MEEMD分解结果的xarray Dataset
    """
    import gc
    if enable_gc:
        # 初始垃圾回收
        gc.collect()
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 提取维度信息
    n_time = len(sst_data.time)
    n_lat = len(sst_data.latitude)
    n_lon = len(sst_data.longitude)
    n_spatial_points = n_lat * n_lon
    
    print(f"重组数据为时空矩阵: {n_spatial_points}空间点 × {n_time}时间点")
    
    # 创建空间点索引到(lat, lon)的映射，用于后续重构
    space_to_latlon = []
    for i in range(n_lat):
        for j in range(n_lon):
            space_to_latlon.append((i, j))
    
    # 创建结果目录
    imf_dir = os.path.join(output_dir, 'imf_components')
    if not os.path.exists(imf_dir):
        os.makedirs(imf_dir)
    
    # 如果使用内存映射，创建临时目录
    temp_dir = os.path.join(output_dir, 'temp')
    if use_memmap and not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    
    # 初始化MEEMD分解器
    meemd = MEEMD2D(ensemble_size=ensemble_size, noise_std=noise_std, n_jobs=n_jobs)
    
    # 批处理参数
    batch_count = int(np.ceil(n_spatial_points / batch_size))
    print(f"将分为{batch_count}批处理，每批{batch_size}个空间点")
    
    # 先进行一小批的计算，确定IMF数量
    print("处理第一小批数据以确定IMF数量...")
    first_batch_size = min(100, n_spatial_points)
    
    # 从原始数据提取第一小批
    first_batch_data = np.zeros((first_batch_size, n_time))
    for i in range(first_batch_size):
        lat_idx, lon_idx = space_to_latlon[i]
        first_batch_data[i, :] = sst_data.sst_standardized.values[:, lat_idx, lon_idx]
    
    # 对第一批进行分解
    first_imfs = meemd.decompose(first_batch_data)
    num_imfs = len(first_imfs)
    print(f"预计每个空间点将得到{num_imfs}个IMF分量")
    
    # 如果使用内存映射，预创建结果数组
    imf_arrays = []
    if use_memmap:
        for i in range(num_imfs):
            memmap_file = os.path.join(temp_dir, f'imf_{i+1}_memmap.dat')
            # 创建内存映射数组，形状为(time, lat, lon)
            imf_array = np.memmap(memmap_file, dtype='float32', mode='w+', 
                                 shape=(n_time, n_lat, n_lon))
            imf_arrays.append(imf_array)
    else:
        # 使用普通数组，但先只分配一个空列表，后续再填充
        imf_arrays = [None] * num_imfs
    
    # 分批处理所有空间点
    for batch_idx in range(batch_count):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, n_spatial_points)
        batch_space_points = range(start_idx, end_idx)
        current_batch_size = end_idx - start_idx
        
        print(f"处理第{batch_idx+1}/{batch_count}批, 空间点{start_idx}到{end_idx-1}")
        
        # 提取当前批次的时间序列
        batch_data = np.zeros((current_batch_size, n_time))
        for i, space_idx in enumerate(batch_space_points):
            lat_idx, lon_idx = space_to_latlon[space_idx]
            batch_data[i, :] = sst_data.sst_standardized.values[:, lat_idx, lon_idx]
        
        # 应用MEEMD分解
        batch_imfs = meemd.decompose(batch_data)
        
        # 将分解结果放回到原始形状
        for imf_idx, imf in enumerate(batch_imfs):
            if imf_idx < num_imfs:  # 防止IMF数量不一致
                # 将每个空间点的IMF分量放回对应的时空位置
                for i, space_idx in enumerate(batch_space_points):
                    lat_idx, lon_idx = space_to_latlon[space_idx]
                    if use_memmap:
                        imf_arrays[imf_idx][:, lat_idx, lon_idx] = imf[i, :]
                    else:
                        # 如果第一次使用，初始化数组
                        if imf_arrays[imf_idx] is None:
                            imf_arrays[imf_idx] = np.zeros((n_time, n_lat, n_lon), dtype='float32')
                        imf_arrays[imf_idx][:, lat_idx, lon_idx] = imf[i, :]
        
        # 手动清理批次数据和结果，释放内存
        del batch_data, batch_imfs
        if enable_gc:
            gc.collect()
    
    # 创建包含所有IMF的Dataset，适合后续模型训练
    print("创建xarray Dataset保存结果...")
    
    # 不一次性创建完整Dataset，而是分别保存每个IMF
    print("分别保存各IMF组件...")
    for i in range(num_imfs):
        print(f"保存IMF {i+1}/{num_imfs}...")
        
        # 创建包含单个IMF的Dataset
        imf_ds = xr.Dataset(
            data_vars={
                f'imf_{i+1}': (['time', 'latitude', 'longitude'], imf_arrays[i]),
            },
            coords={
                'time': sst_data.time.values,
                'latitude': sst_data.latitude.values,
                'longitude': sst_data.longitude.values
            }
        )
        
        # 保存单个IMF组件
        imf_ds.to_netcdf(os.path.join(imf_dir, f'imf_{i+1}.nc'))
        
        # 清理内存
        if use_memmap:
            # 对内存映射文件进行同步写入
            imf_arrays[i].flush()
        else:
            # 释放内存
            del imf_arrays[i]
        
        if enable_gc:
            gc.collect()
    
    # 保存一个元数据文件，记录分解信息
    metadata = {
        'num_imfs': num_imfs,
        'ensemble_size': ensemble_size,
        'noise_std': noise_std,
        'spatial_shape': (n_lat, n_lon),
        'temporal_length': n_time
    }
    np.save(os.path.join(output_dir, 'meemd_metadata.npy'), metadata)
    
    # 合并所有IMF创建完整数据集
    print("合并所有IMF创建完整数据集...")
    all_imfs_ds = xr.Dataset(
        coords={
            'time': sst_data.time.values,
            'latitude': sst_data.latitude.values,
            'longitude': sst_data.longitude.values
        }
    )
    
    # 逐个加载IMF并添加到数据集
    for i in range(num_imfs):
        imf_ds = xr.open_dataset(os.path.join(imf_dir, f'imf_{i+1}.nc'))
        all_imfs_ds[f'imf_{i+1}'] = imf_ds[f'imf_{i+1}']
        imf_ds.close()  # 关闭文件，释放内存
    
    # 保存完整的分解结果
    all_imfs_ds.to_netcdf(os.path.join(output_dir, 'spatiotemporal_meemd_decomposition.nc'))
    print(f"已保存时空MEEMD分解结果到 {os.path.join(output_dir, 'spatiotemporal_meemd_decomposition.nc')}")
    
    # 清理临时文件
    if use_memmap:
        print("清理临时文件...")
        import shutil
        try:
            for i in range(num_imfs):
                # 关闭内存映射
                if hasattr(imf_arrays[i], '_mmap') and imf_arrays[i]._mmap is not None:
                    imf_arrays[i]._mmap.close()
            # 删除临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
        except Exception as e:
            print(f"清理临时文件时出错: {e}")
    
    # 最终垃圾回收
    if enable_gc:
        gc.collect()
    
    return all_imfs_ds

# 可视化分解结果的函数
def visualize_meemd_results(meemd_ds, time_idx=0, output_dir='meemd_plots'):
    """
    可视化MEEMD分解结果
    
    参数:
        meemd_ds: 包含MEEMD分解结果的xarray Dataset
        time_idx: 要可视化的时间索引
        output_dir: 输出目录
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取IMF变量名列表
    imf_vars = [var for var in meemd_ds.data_vars if var.startswith('imf_')]
    
    # 创建总览图
    n_imfs = len(imf_vars)
    time_str = pd.to_datetime(meemd_ds.time.values[time_idx]).strftime('%Y-%m-%d')
    
    # 绘制每个IMF分量
    fig, axs = plt.subplots(n_imfs + 1, 1, figsize=(12, 4 * (n_imfs + 1)))
    
    # 绘制总和（所有IMF的和）
    total_field = sum(meemd_ds[var].isel(time=time_idx) for var in imf_vars)
    im = axs[0].imshow(total_field, cmap='viridis')
    axs[0].set_title(f'所有IMF分量的和 ({time_str})')
    plt.colorbar(im, ax=axs[0])
    
    # 绘制各个IMF分量
    for i, var in enumerate(imf_vars):
        im = axs[i+1].imshow(meemd_ds[var].isel(time=time_idx), cmap='viridis')
        axs[i+1].set_title(f'{var} ({time_str})')
        plt.colorbar(im, ax=axs[i+1])
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'meemd_decomposition_{time_str}.png'))
    plt.close()
    
    print(f"已保存可视化结果到 {os.path.join(output_dir, f'meemd_decomposition_{time_str}.png')}")

# 主函数
def main():
    # 文件路径
    file_path = 'SST-V2.nc'
    
    # 数据预处理
    preprocessed_ds = preprocess_sst_data(file_path)
    
    # 应用时空混合MEEMD分解（内存优化版）
    print("应用时空混合MEEMD分解（内存优化版）...")
    meemd_results = apply_spatiotemporal_meemd_to_sst(
        preprocessed_ds,
        ensemble_size=100,
        noise_std=0.2,
        n_jobs=mp.cpu_count(),      # 使用所有可用CPU
        batch_size=1000,            # 每批处理1000个空间点
        use_memmap=True,            # 使用内存映射
        enable_gc=True              # 启用垃圾回收
    )
    
    # 使用第一个时间点进行可视化
    print("可视化分解结果...")
    visualize_meemd_results(meemd_results, time_idx=0, output_dir='meemd_spatiotemporal_plots')
    
    print("处理完成！")

if __name__ == "__main__":
    main()
