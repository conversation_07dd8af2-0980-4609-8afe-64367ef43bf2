"""
主训练流水线
整合所有模块的完整训练系统
"""

import os
import torch
import numpy as np
import argparse
from datetime import datetime
import json

# 导入自定义模块
from improved_meemd import preprocess_sst_data_advanced, apply_adaptive_meemd_decomposition
from advanced_dataset import create_advanced_dataloaders
from next_gen_model import NextGenSSTModel, CombinedLoss
from advanced_trainer import create_advanced_trainer, plot_training_history

def setup_environment():
    """设置环境"""
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    return device

def create_experiment_config():
    """创建实验配置"""
    config = {
        # 数据配置
        'data': {
            'input_file': 'SST-V2.nc',
            'output_dir': 'meemd_results_v2',
            'quality_control': True
        },
        
        # MEEMD配置
        'meemd': {
            'ensemble_size': 200,
            'max_imf': 8,
            'noise_std': 0.02,
            'use_adaptive': True
        },
        
        # 数据集配置
        'dataset': {
            'input_seq_lens': [7, 7, 14, 14, 21, 21, 30, 30],  # 不同IMF使用不同序列长度
            'pred_seq_len': 1,
            'batch_size': 8,
            'num_workers': 4,
            'cache_data': False,
            'augmentation': True
        },
        
        # 模型配置
        'model': {
            'hidden_dims': [128, 256, 512],
            'kernel_size': 3,
            'dropout': 0.2,
            'use_physics_constraint': True
        },
        
        # 训练配置
        'training': {
            'num_epochs': 100,
            'learning_rate': 1e-4,
            'weight_decay': 1e-4,
            'scheduler_type': 'cosine',  # 'cosine' or 'plateau'
            'max_grad_norm': 1.0,
            'patience': 15
        },
        
        # 损失函数配置
        'loss': {
            'mse_weight': 0.6,
            'l1_weight': 0.3,
            'physics_weight': 0.1,
            'imf_weights': [1.0, 1.0, 0.8, 0.8, 0.6, 0.6, 0.4, 0.4]  # 高频IMF权重更大
        }
    }
    
    return config

def run_data_preprocessing(config):
    """运行数据预处理"""
    print("=" * 60)
    print("阶段1: 数据预处理")
    print("=" * 60)
    
    # 检查是否已存在预处理结果
    if os.path.exists(config['data']['output_dir']):
        print(f"检测到已存在的MEEMD结果: {config['data']['output_dir']}")
        response = input("是否重新进行MEEMD分解? (y/n): ")
        if response.lower() != 'y':
            print("跳过数据预处理阶段")
            return
    
    # 预处理SST数据
    print("开始SST数据预处理...")
    sst_data = preprocess_sst_data_advanced(
        config['data']['input_file'],
        quality_control=config['data']['quality_control']
    )
    
    # 应用自适应MEEMD分解
    print("开始自适应MEEMD分解...")
    imfs, quality_metrics, metadata = apply_adaptive_meemd_decomposition(
        sst_data,
        output_dir=config['data']['output_dir'],
        ensemble_size=config['meemd']['ensemble_size'],
        max_imf=config['meemd']['max_imf']
    )
    
    print(f"MEEMD分解完成，质量分数: {quality_metrics['quality_score']:.4f}")
    
    # 更新配置中的IMF数量
    config['meemd']['actual_num_imfs'] = metadata['num_imfs']
    
    return sst_data, imfs, quality_metrics

def create_model_and_criterion(config, device):
    """创建模型和损失函数"""
    print("=" * 60)
    print("阶段2: 模型创建")
    print("=" * 60)
    
    num_imfs = config['meemd'].get('actual_num_imfs', config['meemd']['max_imf'])
    
    # 创建模型
    model = NextGenSSTModel(
        num_imfs=num_imfs,
        hidden_dims=config['model']['hidden_dims'],
        kernel_size=config['model']['kernel_size'],
        imf_seq_lens=config['dataset']['input_seq_lens'][:num_imfs],
        dropout=config['model']['dropout'],
        use_physics_constraint=config['model']['use_physics_constraint']
    ).to(device)
    
    # 创建损失函数
    criterion = CombinedLoss(
        mse_weight=config['loss']['mse_weight'],
        l1_weight=config['loss']['l1_weight'],
        physics_weight=config['loss']['physics_weight'],
        imf_weights=config['loss']['imf_weights'][:num_imfs]
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型创建完成:")
    print(f"  总参数数量: {total_params:,}")
    print(f"  可训练参数: {trainable_params:,}")
    print(f"  IMF数量: {num_imfs}")
    print(f"  序列长度配置: {config['dataset']['input_seq_lens'][:num_imfs]}")
    
    return model, criterion

def run_training(config, model, criterion, device):
    """运行训练"""
    print("=" * 60)
    print("阶段3: 模型训练")
    print("=" * 60)
    
    # 创建数据加载器
    print("创建数据加载器...")
    train_loader, val_loader, test_loader = create_advanced_dataloaders(
        imf_dir=config['data']['output_dir'],
        batch_size=config['dataset']['batch_size'],
        input_seq_lens=config['dataset']['input_seq_lens'],
        pred_seq_len=config['dataset']['pred_seq_len'],
        num_workers=config['dataset']['num_workers'],
        cache_data=config['dataset']['cache_data'],
        augmentation=config['dataset']['augmentation']
    )
    
    # 创建训练器
    print("创建高级训练器...")
    trainer = create_advanced_trainer(
        model=model,
        criterion=criterion,
        device=device,
        learning_rate=config['training']['learning_rate'],
        weight_decay=config['training']['weight_decay'],
        scheduler_type=config['training']['scheduler_type'],
        max_grad_norm=config['training']['max_grad_norm'],
        patience=config['training']['patience']
    )
    
    # 开始训练
    print("开始训练...")
    training_metrics = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['training']['num_epochs']
    )
    
    # 绘制训练历史
    plot_training_history(training_metrics, 'training_history_v2.png')
    
    return trainer, training_metrics, test_loader

def evaluate_model(trainer, test_loader, device):
    """评估模型"""
    print("=" * 60)
    print("阶段4: 模型评估")
    print("=" * 60)
    
    # 加载最佳模型
    best_model_path = os.path.join(trainer.checkpoint_dir, 'best_model.pth')
    if os.path.exists(best_model_path):
        trainer.load_checkpoint(best_model_path)
        print("已加载最佳模型进行评估")
    
    # 在测试集上评估
    test_metrics = trainer.validate_epoch(test_loader)
    
    print("测试集评估结果:")
    for key, value in test_metrics.items():
        print(f"  {key}: {value:.6f}")
    
    return test_metrics

def save_experiment_results(config, training_metrics, test_metrics):
    """保存实验结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"experiment_results_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存配置
    config_path = os.path.join(results_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    # 保存训练指标
    training_path = os.path.join(results_dir, 'training_metrics.json')
    with open(training_path, 'w') as f:
        json.dump(training_metrics, f, indent=2)
    
    # 保存测试结果
    test_path = os.path.join(results_dir, 'test_metrics.json')
    with open(test_path, 'w') as f:
        json.dump(test_metrics, f, indent=2)
    
    # 创建结果摘要
    summary = {
        'experiment_time': timestamp,
        'best_val_loss': min(training_metrics.get('val_total', [float('inf')])),
        'final_test_metrics': test_metrics,
        'model_config': config['model'],
        'training_config': config['training']
    }
    
    summary_path = os.path.join(results_dir, 'experiment_summary.json')
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"实验结果已保存到: {results_dir}")
    return results_dir

def main():
    """主函数"""
    print("=" * 80)
    print("MEEMD-ConvLSTM 海表温度预测系统 v2.0")
    print("基于分解-预测-重构思路的下一代预测系统")
    print("=" * 80)
    
    # 设置环境
    device = setup_environment()
    
    # 创建实验配置
    config = create_experiment_config()
    
    try:
        # 阶段1: 数据预处理
        preprocessing_result = run_data_preprocessing(config)
        
        # 阶段2: 创建模型
        model, criterion = create_model_and_criterion(config, device)
        
        # 阶段3: 训练模型
        trainer, training_metrics, test_loader = run_training(config, model, criterion, device)
        
        # 阶段4: 评估模型
        test_metrics = evaluate_model(trainer, test_loader, device)
        
        # 保存实验结果
        results_dir = save_experiment_results(config, training_metrics, test_metrics)
        
        print("=" * 80)
        print("实验完成！")
        print(f"最佳验证损失: {min(training_metrics.get('val_total', [float('inf')])):.6f}")
        print(f"测试MAE: {test_metrics.get('val_mae', 0):.6f}")
        print(f"测试RMSE: {test_metrics.get('val_rmse', 0):.6f}")
        print(f"结果保存在: {results_dir}")
        print("=" * 80)
        
    except Exception as e:
        print(f"实验过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='MEEMD-ConvLSTM SST预测系统')
    parser.add_argument('--config', type=str, help='配置文件路径（可选）')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径（可选）')
    parser.add_argument('--eval-only', action='store_true', help='仅评估模式')
    
    args = parser.parse_args()
    
    # 如果提供了配置文件，加载它
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
        print(f"已加载配置文件: {args.config}")
    
    main()
