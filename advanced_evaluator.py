"""
高级评估器
提供全面的模型评估和可视化功能
"""

import os
import numpy as np
import torch
import torch.nn.functional as F
import xarray as xr
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import mean_absolute_error, mean_squared_error
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from typing import Dict, List, Tuple, Optional
import json
from tqdm import tqdm

class ComprehensiveEvaluator:
    """综合评估器"""
    
    def __init__(self, model: torch.nn.Module, device: torch.device,
                 lats: np.ndarray, lons: np.ndarray, 
                 output_dir: str = 'evaluation_results_v2'):
        self.model = model
        self.device = device
        self.lats = lats
        self.lons = lons
        self.output_dir = output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 评估指标存储
        self.evaluation_results = {}
        
    def predict_batch(self, data_loader) -> <PERSON>ple[np.ndarray, np.ndarray]:
        """批量预测"""
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in tqdm(data_loader, desc="预测中"):
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                # 模型预测
                outputs = self.model(inputs)
                
                # 转换为numpy
                predictions = outputs.cpu().numpy()
                targets_np = targets[:, :, 0].cpu().numpy()  # 移除时间维度
                
                all_predictions.append(predictions)
                all_targets.append(targets_np)
        
        # 合并所有批次
        predictions = np.concatenate(all_predictions, axis=0)
        targets = np.concatenate(all_targets, axis=0)
        
        return predictions, targets
    
    def compute_basic_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict[str, float]:
        """计算基础评估指标"""
        # 重构总SST
        pred_sst = np.sum(predictions, axis=1)  # [batch, height, width]
        target_sst = np.sum(targets, axis=1)
        
        # 展平用于计算指标
        pred_flat = pred_sst.flatten()
        target_flat = target_sst.flatten()
        
        # 移除NaN值
        valid_mask = ~(np.isnan(pred_flat) | np.isnan(target_flat))
        pred_valid = pred_flat[valid_mask]
        target_valid = target_flat[valid_mask]
        
        # 计算指标
        mae = mean_absolute_error(target_valid, pred_valid)
        mse = mean_squared_error(target_valid, pred_valid)
        rmse = np.sqrt(mse)
        
        # 相关系数
        correlation = np.corrcoef(pred_valid, target_valid)[0, 1]
        
        # R²分数
        ss_res = np.sum((target_valid - pred_valid) ** 2)
        ss_tot = np.sum((target_valid - np.mean(target_valid)) ** 2)
        r2_score = 1 - (ss_res / ss_tot)
        
        # 相对误差
        relative_mae = mae / np.mean(np.abs(target_valid))
        relative_rmse = rmse / np.std(target_valid)
        
        metrics = {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'correlation': correlation,
            'r2_score': r2_score,
            'relative_mae': relative_mae,
            'relative_rmse': relative_rmse,
            'bias': np.mean(pred_valid - target_valid),
            'std_error': np.std(pred_valid - target_valid)
        }
        
        return metrics
    
    def compute_spatial_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict[str, np.ndarray]:
        """计算空间分布指标"""
        # 重构总SST
        pred_sst = np.sum(predictions, axis=1)
        target_sst = np.sum(targets, axis=1)
        
        # 计算每个空间点的指标
        spatial_mae = np.mean(np.abs(pred_sst - target_sst), axis=0)
        spatial_rmse = np.sqrt(np.mean((pred_sst - target_sst) ** 2, axis=0))
        spatial_bias = np.mean(pred_sst - target_sst, axis=0)
        
        # 计算空间相关性
        spatial_corr = np.zeros_like(spatial_mae)
        for i in range(spatial_mae.shape[0]):
            for j in range(spatial_mae.shape[1]):
                pred_series = pred_sst[:, i, j]
                target_series = target_sst[:, i, j]
                
                # 移除NaN值
                valid_mask = ~(np.isnan(pred_series) | np.isnan(target_series))
                if np.sum(valid_mask) > 10:  # 至少10个有效点
                    corr = np.corrcoef(pred_series[valid_mask], target_series[valid_mask])[0, 1]
                    spatial_corr[i, j] = corr if not np.isnan(corr) else 0
        
        return {
            'spatial_mae': spatial_mae,
            'spatial_rmse': spatial_rmse,
            'spatial_bias': spatial_bias,
            'spatial_correlation': spatial_corr
        }
    
    def compute_imf_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict[str, List[float]]:
        """计算各IMF分量的指标"""
        num_imfs = predictions.shape[1]
        imf_metrics = {
            'imf_mae': [],
            'imf_rmse': [],
            'imf_correlation': [],
            'imf_energy_ratio': []
        }
        
        for imf_idx in range(num_imfs):
            pred_imf = predictions[:, imf_idx].flatten()
            target_imf = targets[:, imf_idx].flatten()
            
            # 移除NaN值
            valid_mask = ~(np.isnan(pred_imf) | np.isnan(target_imf))
            pred_valid = pred_imf[valid_mask]
            target_valid = target_imf[valid_mask]
            
            if len(pred_valid) > 0:
                mae = mean_absolute_error(target_valid, pred_valid)
                rmse = np.sqrt(mean_squared_error(target_valid, pred_valid))
                corr = np.corrcoef(pred_valid, target_valid)[0, 1] if len(pred_valid) > 1 else 0
                
                # 能量比（方差比）
                energy_ratio = np.var(pred_valid) / (np.var(target_valid) + 1e-8)
                
                imf_metrics['imf_mae'].append(mae)
                imf_metrics['imf_rmse'].append(rmse)
                imf_metrics['imf_correlation'].append(corr if not np.isnan(corr) else 0)
                imf_metrics['imf_energy_ratio'].append(energy_ratio)
            else:
                imf_metrics['imf_mae'].append(0)
                imf_metrics['imf_rmse'].append(0)
                imf_metrics['imf_correlation'].append(0)
                imf_metrics['imf_energy_ratio'].append(0)
        
        return imf_metrics
    
    def plot_spatial_error_maps(self, spatial_metrics: Dict[str, np.ndarray]):
        """绘制空间误差分布图"""
        fig, axes = plt.subplots(2, 2, figsize=(20, 16), 
                                subplot_kw={'projection': ccrs.PlateCarree()})
        
        metrics_to_plot = [
            ('spatial_mae', 'MAE (°C)', 'Reds'),
            ('spatial_rmse', 'RMSE (°C)', 'Oranges'),
            ('spatial_bias', 'Bias (°C)', 'RdBu_r'),
            ('spatial_correlation', 'Correlation', 'viridis')
        ]
        
        for idx, (metric_name, title, cmap) in enumerate(metrics_to_plot):
            ax = axes[idx // 2, idx % 2]
            data = spatial_metrics[metric_name]
            
            # 设置地图特征
            ax.add_feature(cfeature.COASTLINE)
            ax.add_feature(cfeature.BORDERS)
            ax.add_feature(cfeature.LAND, alpha=0.3)
            ax.add_feature(cfeature.OCEAN, alpha=0.3)
            
            # 绘制数据
            if metric_name == 'spatial_bias':
                vmax = np.nanmax(np.abs(data))
                im = ax.contourf(self.lons, self.lats, data, levels=20, 
                               cmap=cmap, vmin=-vmax, vmax=vmax, transform=ccrs.PlateCarree())
            else:
                im = ax.contourf(self.lons, self.lats, data, levels=20, 
                               cmap=cmap, transform=ccrs.PlateCarree())
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.05)
            cbar.set_label(title)
            
            ax.set_title(f'空间分布 - {title}')
            ax.set_global()
            ax.gridlines(draw_labels=True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'spatial_error_maps.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_imf_analysis(self, imf_metrics: Dict[str, List[float]]):
        """绘制IMF分析图"""
        num_imfs = len(imf_metrics['imf_mae'])
        imf_indices = range(1, num_imfs + 1)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # MAE对比
        axes[0, 0].bar(imf_indices, imf_metrics['imf_mae'], alpha=0.7, color='red')
        axes[0, 0].set_title('各IMF分量的MAE')
        axes[0, 0].set_xlabel('IMF分量')
        axes[0, 0].set_ylabel('MAE')
        axes[0, 0].grid(True, alpha=0.3)
        
        # RMSE对比
        axes[0, 1].bar(imf_indices, imf_metrics['imf_rmse'], alpha=0.7, color='orange')
        axes[0, 1].set_title('各IMF分量的RMSE')
        axes[0, 1].set_xlabel('IMF分量')
        axes[0, 1].set_ylabel('RMSE')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 相关性对比
        axes[1, 0].bar(imf_indices, imf_metrics['imf_correlation'], alpha=0.7, color='blue')
        axes[1, 0].set_title('各IMF分量的相关性')
        axes[1, 0].set_xlabel('IMF分量')
        axes[1, 0].set_ylabel('相关系数')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_ylim(0, 1)
        
        # 能量比对比
        axes[1, 1].bar(imf_indices, imf_metrics['imf_energy_ratio'], alpha=0.7, color='green')
        axes[1, 1].axhline(y=1, color='red', linestyle='--', alpha=0.7, label='理想值')
        axes[1, 1].set_title('各IMF分量的能量比')
        axes[1, 1].set_xlabel('IMF分量')
        axes[1, 1].set_ylabel('能量比 (预测/真实)')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'imf_analysis.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_prediction_samples(self, predictions: np.ndarray, targets: np.ndarray, 
                              num_samples: int = 6):
        """绘制预测样本对比"""
        # 重构总SST
        pred_sst = np.sum(predictions, axis=1)
        target_sst = np.sum(targets, axis=1)
        
        # 随机选择样本
        sample_indices = np.random.choice(len(pred_sst), num_samples, replace=False)
        
        fig, axes = plt.subplots(num_samples, 3, figsize=(18, 6*num_samples))
        
        for i, sample_idx in enumerate(sample_indices):
            pred_sample = pred_sst[sample_idx]
            target_sample = target_sst[sample_idx]
            error = pred_sample - target_sample
            
            # 真实值
            im1 = axes[i, 0].imshow(target_sample, cmap='coolwarm', aspect='auto')
            axes[i, 0].set_title(f'样本 {sample_idx+1} - 真实SST')
            plt.colorbar(im1, ax=axes[i, 0], shrink=0.8)
            
            # 预测值
            im2 = axes[i, 1].imshow(pred_sample, cmap='coolwarm', aspect='auto')
            axes[i, 1].set_title(f'样本 {sample_idx+1} - 预测SST')
            plt.colorbar(im2, ax=axes[i, 1], shrink=0.8)
            
            # 误差
            vmax = np.nanmax(np.abs(error))
            im3 = axes[i, 2].imshow(error, cmap='RdBu_r', vmin=-vmax, vmax=vmax, aspect='auto')
            axes[i, 2].set_title(f'样本 {sample_idx+1} - 预测误差')
            plt.colorbar(im3, ax=axes[i, 2], shrink=0.8)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'prediction_samples.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_evaluation_report(self, basic_metrics: Dict[str, float],
                                 spatial_metrics: Dict[str, np.ndarray],
                                 imf_metrics: Dict[str, List[float]]):
        """生成评估报告"""
        report = {
            'basic_metrics': basic_metrics,
            'spatial_statistics': {
                'mean_spatial_mae': float(np.nanmean(spatial_metrics['spatial_mae'])),
                'std_spatial_mae': float(np.nanstd(spatial_metrics['spatial_mae'])),
                'mean_spatial_rmse': float(np.nanmean(spatial_metrics['spatial_rmse'])),
                'std_spatial_rmse': float(np.nanstd(spatial_metrics['spatial_rmse'])),
                'mean_spatial_correlation': float(np.nanmean(spatial_metrics['spatial_correlation'])),
                'std_spatial_correlation': float(np.nanstd(spatial_metrics['spatial_correlation']))
            },
            'imf_statistics': {
                'mean_imf_mae': float(np.mean(imf_metrics['imf_mae'])),
                'std_imf_mae': float(np.std(imf_metrics['imf_mae'])),
                'mean_imf_correlation': float(np.mean(imf_metrics['imf_correlation'])),
                'std_imf_correlation': float(np.std(imf_metrics['imf_correlation'])),
                'imf_details': imf_metrics
            }
        }
        
        # 保存报告
        report_path = os.path.join(self.output_dir, 'evaluation_report.json')
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # 生成文本报告
        text_report_path = os.path.join(self.output_dir, 'evaluation_summary.txt')
        with open(text_report_path, 'w') as f:
            f.write("MEEMD-ConvLSTM 模型评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("基础指标:\n")
            f.write(f"  MAE: {basic_metrics['mae']:.4f}°C\n")
            f.write(f"  RMSE: {basic_metrics['rmse']:.4f}°C\n")
            f.write(f"  相关系数: {basic_metrics['correlation']:.4f}\n")
            f.write(f"  R²分数: {basic_metrics['r2_score']:.4f}\n")
            f.write(f"  相对MAE: {basic_metrics['relative_mae']:.4f}\n")
            f.write(f"  偏差: {basic_metrics['bias']:.4f}°C\n\n")
            
            f.write("空间统计:\n")
            f.write(f"  平均空间MAE: {report['spatial_statistics']['mean_spatial_mae']:.4f}°C\n")
            f.write(f"  平均空间RMSE: {report['spatial_statistics']['mean_spatial_rmse']:.4f}°C\n")
            f.write(f"  平均空间相关性: {report['spatial_statistics']['mean_spatial_correlation']:.4f}\n\n")
            
            f.write("IMF分量统计:\n")
            for i, (mae, corr) in enumerate(zip(imf_metrics['imf_mae'], imf_metrics['imf_correlation'])):
                f.write(f"  IMF_{i+1}: MAE={mae:.4f}, 相关性={corr:.4f}\n")
        
        print(f"评估报告已保存到: {self.output_dir}")
        return report
    
    def comprehensive_evaluate(self, data_loader) -> Dict:
        """综合评估"""
        print("开始综合评估...")
        
        # 批量预测
        predictions, targets = self.predict_batch(data_loader)
        
        # 计算各种指标
        print("计算基础指标...")
        basic_metrics = self.compute_basic_metrics(predictions, targets)
        
        print("计算空间指标...")
        spatial_metrics = self.compute_spatial_metrics(predictions, targets)
        
        print("计算IMF指标...")
        imf_metrics = self.compute_imf_metrics(predictions, targets)
        
        # 生成可视化
        print("生成可视化图表...")
        self.plot_spatial_error_maps(spatial_metrics)
        self.plot_imf_analysis(imf_metrics)
        self.plot_prediction_samples(predictions, targets)
        
        # 生成报告
        print("生成评估报告...")
        report = self.generate_evaluation_report(basic_metrics, spatial_metrics, imf_metrics)
        
        print("综合评估完成！")
        return report

if __name__ == "__main__":
    # 测试评估器
    print("=== 测试综合评估器 ===")
    pass
