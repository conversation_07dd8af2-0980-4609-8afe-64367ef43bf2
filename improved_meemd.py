"""
改进的MEEMD分解模块
基于分解-预测-重构思路的优化实现
"""

import numpy as np
import xarray as xr
from PyEMD import EEMD
import multiprocessing as mp
from functools import partial
import gc
import warnings
warnings.filterwarnings('ignore')

class AdaptiveMEEMD2D:
    """
    自适应二维MEEMD分解器
    针对海表温度数据优化的分解算法
    """
    
    def __init__(self, ensemble_size=200, noise_std=0.02, max_imf=8, 
                 n_jobs=-1, quality_threshold=0.95):
        """
        初始化自适应MEEMD分解器
        
        参数:
            ensemble_size: 集成数量，增加到200提高稳定性
            noise_std: 噪声标准差，降低到0.02减少伪影
            max_imf: 最大IMF数量
            n_jobs: 并行进程数
            quality_threshold: 分解质量阈值
        """
        self.ensemble_size = ensemble_size
        self.noise_std = noise_std
        self.max_imf = max_imf
        self.n_jobs = n_jobs if n_jobs > 0 else mp.cpu_count()
        self.quality_threshold = quality_threshold
        
        # 分解质量评估指标
        self.decomposition_quality = {}
        
    def _adaptive_noise_std(self, signal):
        """
        根据信号特征自适应调整噪声标准差
        """
        signal_std = np.std(signal)
        signal_range = np.ptp(signal)  # peak-to-peak
        
        # 基于信号变异性调整噪声
        adaptive_noise = min(0.05, max(0.01, signal_std * 0.1))
        return adaptive_noise
    
    def _perform_1d_eemd(self, signal, adaptive_noise=True):
        """
        执行一维EEMD分解，支持自适应噪声
        """
        if np.all(np.isnan(signal)) or len(np.unique(signal[~np.isnan(signal)])) < 3:
            # 处理无效信号
            return np.zeros((self.max_imf, len(signal)))
        
        # 插值处理缺失值
        valid_mask = ~np.isnan(signal)
        if not np.all(valid_mask):
            signal = np.interp(np.arange(len(signal)), 
                             np.where(valid_mask)[0], 
                             signal[valid_mask])
        
        # 自适应噪声标准差
        noise_std = self._adaptive_noise_std(signal) if adaptive_noise else self.noise_std
        
        # 执行EEMD分解
        eemd = EEMD(trials=self.ensemble_size, noise_width=noise_std, 
                   ext_EMD=None, parallel=False)  # 在外层并行
        
        try:
            imfs = eemd.eemd(signal, max_imf=self.max_imf)
            
            # 确保IMF数量一致
            if len(imfs) < self.max_imf:
                # 补零到指定数量
                padding = np.zeros((self.max_imf - len(imfs), len(signal)))
                imfs = np.vstack([imfs, padding])
            elif len(imfs) > self.max_imf:
                # 合并多余的IMF到最后一个
                imfs = np.vstack([imfs[:self.max_imf-1], 
                                np.sum(imfs[self.max_imf-1:], axis=0, keepdims=True)])
            
            return imfs
            
        except Exception as e:
            print(f"EEMD分解失败: {e}")
            # 返回零矩阵作为备选
            return np.zeros((self.max_imf, len(signal)))
    
    def _decompose_spatial_point(self, args):
        """
        分解单个空间点的时间序列
        """
        point_idx, time_series = args
        return point_idx, self._perform_1d_eemd(time_series)
    
    def decompose(self, data_2d, use_adaptive=True, save_quality_metrics=True):
        """
        对二维时空数据进行MEEMD分解
        
        参数:
            data_2d: 输入数据 [time, lat, lon]
            use_adaptive: 是否使用自适应参数
            save_quality_metrics: 是否保存质量指标
            
        返回:
            imfs: 分解结果 [num_imfs, time, lat, lon]
            quality_metrics: 分解质量指标
        """
        print(f"开始自适应MEEMD分解，数据形状: {data_2d.shape}")
        
        time_steps, n_lat, n_lon = data_2d.shape
        n_spatial_points = n_lat * n_lon
        
        # 重组数据为 [spatial_points, time]
        reshaped_data = data_2d.reshape(time_steps, -1).T
        
        # 准备并行处理的参数
        args_list = [(i, reshaped_data[i]) for i in range(n_spatial_points)]
        
        # 并行分解
        print(f"使用 {self.n_jobs} 个进程进行并行分解...")
        with mp.Pool(processes=self.n_jobs) as pool:
            results = pool.map(self._decompose_spatial_point, args_list)
        
        # 整理结果
        imfs_array = np.zeros((self.max_imf, time_steps, n_spatial_points))
        for point_idx, point_imfs in results:
            imfs_array[:, :, point_idx] = point_imfs
        
        # 重构为原始空间形状
        imfs = imfs_array.reshape(self.max_imf, time_steps, n_lat, n_lon)
        
        # 计算分解质量指标
        quality_metrics = {}
        if save_quality_metrics:
            quality_metrics = self._evaluate_decomposition_quality(data_2d, imfs)
            self.decomposition_quality = quality_metrics
        
        print(f"MEEMD分解完成，生成 {self.max_imf} 个IMF分量")
        return imfs, quality_metrics
    
    def _evaluate_decomposition_quality(self, original, imfs):
        """
        评估分解质量
        """
        # 重构信号
        reconstructed = np.sum(imfs, axis=0)
        
        # 计算重构误差
        reconstruction_error = np.mean((original - reconstructed) ** 2)
        relative_error = reconstruction_error / np.var(original)
        
        # 计算IMF正交性
        imf_correlations = []
        for i in range(len(imfs)):
            for j in range(i+1, len(imfs)):
                corr = np.corrcoef(imfs[i].flatten(), imfs[j].flatten())[0, 1]
                imf_correlations.append(abs(corr))
        
        max_correlation = max(imf_correlations) if imf_correlations else 0
        
        # 计算IMF能量分布
        imf_energies = [np.var(imf) for imf in imfs]
        total_energy = sum(imf_energies)
        energy_distribution = [e/total_energy for e in imf_energies]
        
        # 计算频率分离度
        frequency_separation = self._compute_frequency_separation(imfs)
        
        quality_metrics = {
            'reconstruction_error': reconstruction_error,
            'relative_error': relative_error,
            'max_imf_correlation': max_correlation,
            'energy_distribution': energy_distribution,
            'frequency_separation': frequency_separation,
            'quality_score': 1 - relative_error - max_correlation  # 综合质量分数
        }
        
        return quality_metrics
    
    def _compute_frequency_separation(self, imfs):
        """
        计算IMF频率分离度
        """
        # 简化的频率分离度计算
        # 基于相邻IMF的能量比
        separations = []
        for i in range(len(imfs)-1):
            energy_i = np.var(imfs[i])
            energy_next = np.var(imfs[i+1])
            if energy_next > 0:
                separation = energy_i / energy_next
                separations.append(separation)
        
        return np.mean(separations) if separations else 0

def preprocess_sst_data_advanced(file_path, target_shape=None, quality_control=True):
    """
    高级SST数据预处理
    
    参数:
        file_path: NetCDF文件路径
        target_shape: 目标空间形状 (lat, lon)
        quality_control: 是否进行质量控制
    """
    print(f"加载SST数据: {file_path}")
    
    # 加载数据
    ds = xr.open_dataset(file_path)
    
    # 确定SST变量名
    sst_var = None
    for var in ['analysed_sst', 'sst', 'SST']:
        if var in ds.data_vars:
            sst_var = var
            break
    
    if sst_var is None:
        raise ValueError("未找到SST变量")
    
    sst = ds[sst_var].values
    lats = ds.latitude.values
    lons = ds.longitude.values
    times = ds.time.values
    
    print(f"原始数据形状: {sst.shape}")
    print(f"时间范围: {times[0]} 到 {times[-1]}")
    print(f"空间范围: 纬度 {lats.min():.2f}° 到 {lats.max():.2f}°")
    print(f"空间范围: 经度 {lons.min():.2f}° 到 {lons.max():.2f}°")
    
    # 温度单位转换
    if np.mean(sst[~np.isnan(sst)]) > 100:  # 可能是Kelvin
        print("检测到Kelvin单位，转换为Celsius")
        sst = sst - 273.15
    
    # 质量控制
    if quality_control:
        print("执行数据质量控制...")
        
        # 移除异常值
        valid_range = (-5, 40)  # 合理的SST范围
        sst = np.where((sst < valid_range[0]) | (sst > valid_range[1]), np.nan, sst)
        
        # 计算数据质量统计
        total_points = sst.size
        valid_points = np.sum(~np.isnan(sst))
        valid_ratio = valid_points / total_points
        
        print(f"数据质量: {valid_ratio:.2%} 有效数据点")
        
        if valid_ratio < 0.8:
            print("警告: 有效数据点比例较低，可能影响分析质量")
    
    # 空间重采样（如果需要）
    if target_shape and (len(lats), len(lons)) != target_shape:
        print(f"重采样到目标形状: {target_shape}")
        # 这里可以添加空间插值代码
        pass
    
    # 高级标准化：分区域标准化
    print("执行分区域标准化...")
    sst_normalized = np.zeros_like(sst)
    
    for lat_idx in range(len(lats)):
        for lon_idx in range(len(lons)):
            pixel_series = sst[:, lat_idx, lon_idx]
            valid_mask = ~np.isnan(pixel_series)
            
            if np.sum(valid_mask) > len(pixel_series) * 0.5:  # 至少50%有效数据
                pixel_mean = np.mean(pixel_series[valid_mask])
                pixel_std = np.std(pixel_series[valid_mask])
                
                if pixel_std > 0:
                    sst_normalized[:, lat_idx, lon_idx] = (pixel_series - pixel_mean) / pixel_std
                else:
                    sst_normalized[:, lat_idx, lon_idx] = pixel_series - pixel_mean
            else:
                sst_normalized[:, lat_idx, lon_idx] = np.nan
    
    # 数据集划分
    n_time = len(times)
    train_end = int(n_time * 0.8)
    val_end = int(n_time * 0.9)
    
    train_data = sst_normalized[:train_end]
    val_data = sst_normalized[train_end:val_end]
    test_data = sst_normalized[val_end:]
    
    print(f"数据集划分:")
    print(f"  训练集: {train_data.shape[0]} 时间步")
    print(f"  验证集: {val_data.shape[0]} 时间步")
    print(f"  测试集: {test_data.shape[0]} 时间步")
    
    # 保存预处理结果
    metadata = {
        'original_shape': sst.shape,
        'time_range': (str(times[0]), str(times[-1])),
        'spatial_range': {
            'lat_min': float(lats.min()), 'lat_max': float(lats.max()),
            'lon_min': float(lons.min()), 'lon_max': float(lons.max())
        },
        'valid_data_ratio': float(valid_ratio),
        'normalization_method': 'pixel_wise_standardization'
    }
    
    ds.close()
    
    return {
        'train_data': train_data,
        'val_data': val_data, 
        'test_data': test_data,
        'full_data': sst_normalized,
        'lats': lats,
        'lons': lons,
        'times': times,
        'metadata': metadata
    }

def apply_adaptive_meemd_decomposition(sst_data, output_dir='meemd_results_v2', 
                                     ensemble_size=200, max_imf=8):
    """
    应用自适应MEEMD分解到SST数据
    """
    import os
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 初始化自适应MEEMD分解器
    meemd = AdaptiveMEEMD2D(
        ensemble_size=ensemble_size,
        noise_std=0.02,  # 较小的噪声标准差
        max_imf=max_imf,
        n_jobs=-1,
        quality_threshold=0.95
    )
    
    # 执行分解
    train_data = sst_data['train_data']
    imfs, quality_metrics = meemd.decompose(train_data, use_adaptive=True)
    
    print("\n=== 分解质量评估 ===")
    print(f"重构误差: {quality_metrics['reconstruction_error']:.6f}")
    print(f"相对误差: {quality_metrics['relative_error']:.4f}")
    print(f"IMF最大相关性: {quality_metrics['max_imf_correlation']:.4f}")
    print(f"综合质量分数: {quality_metrics['quality_score']:.4f}")
    
    # 保存IMF分量
    lats = sst_data['lats']
    lons = sst_data['lons']
    times = sst_data['times'][:len(train_data)]
    
    for i in range(max_imf):
        imf_ds = xr.Dataset({
            f'imf_{i+1}': (['time', 'latitude', 'longitude'], imfs[i])
        }, coords={
            'time': times,
            'latitude': lats,
            'longitude': lons
        })
        
        output_path = os.path.join(output_dir, f'imf_{i+1}.nc')
        imf_ds.to_netcdf(output_path)
        print(f"保存 IMF_{i+1} 到 {output_path}")
    
    # 保存元数据
    enhanced_metadata = sst_data['metadata'].copy()
    enhanced_metadata.update({
        'num_imfs': max_imf,
        'ensemble_size': ensemble_size,
        'meemd_version': 'adaptive_v2',
        'decomposition_quality': quality_metrics
    })
    
    metadata_path = os.path.join(output_dir, 'meemd_metadata_v2.npy')
    np.save(metadata_path, enhanced_metadata)
    print(f"保存元数据到 {metadata_path}")
    
    return imfs, quality_metrics, enhanced_metadata

if __name__ == "__main__":
    # 示例使用
    print("=== 改进的MEEMD分解系统 ===")
    
    # 预处理数据
    sst_data = preprocess_sst_data_advanced('SST-V2.nc', quality_control=True)
    
    # 执行自适应MEEMD分解
    imfs, quality, metadata = apply_adaptive_meemd_decomposition(
        sst_data, 
        output_dir='meemd_results_v2',
        ensemble_size=200,
        max_imf=8
    )
    
    print("分解完成！")
